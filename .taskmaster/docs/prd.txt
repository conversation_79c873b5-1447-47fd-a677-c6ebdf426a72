# Product Requirements Document (PRD)
## Bid Management Feature

## Overview
The Bid Management feature allows service providers to submit bids on customer service requests, customers to view and manage these bids, and administrators to oversee all bid activities across the platform. This document outlines the requirements for implementing the frontend components that will interact with the existing backend API.

## User Roles and Permissions
1. **Service Provider**
   - Create bids for open service requests
   - View, update, and withdraw their own bids
   - Track status of submitted bids

2. **Customer**
   - View bids received for their service requests
   - Accept or reject bids
   - Filter and sort received bids

3. **Administrator**
   - View all bids across the platform
   - Access comprehensive bid statistics
   - Advanced filtering and sorting capabilities

## Feature Requirements

### 1. Provider Bid Management

#### 1.1 Bid Creation
- Providers can browse available service requests and submit bids
- Bid submission form should include:
  - Bid amount field (currency input)
  - Detailed description text area for proposal details
  - Submit button
- System should validate input (numeric amount, description length)
- Confirmation notification upon successful bid creation

#### 1.2 Provider Bid Dashboard
- Dedicated dashboard section showing all bids submitted by the provider
- Filter options:
  - By status (requested, accepted, rejected)
  - By date range
  - By service request
- Sort options (amount, date, status)
- Each bid card/row should display:
  - Service request title
  - Bid amount
  - Submission date
  - Current status with appropriate visual indicators
  - Actions available based on status

#### 1.3 Bid Management Actions
- Providers can view detailed information about each bid
- Update functionality for pending bids (description, amount)
- Withdraw option for pending bids only
- Disabled actions for accepted/rejected bids

### 2. Customer Bid Management

#### 2.1 Bid Viewing Interface
- Service request detail page should show all received bids
- Each bid should display:
  - Provider name and basic profile information
  - Bid amount
  - Proposal description
  - Submission date
  - Status indicators
  - Action buttons (accept/reject)

#### 2.2 Bid Comparison Tools
- Sort bids by amount, submission date
- Visual comparison of bid amounts against original service request budget
- Provider rating/reviews if available

#### 2.3 Bid Response Actions
- Accept bid button with confirmation dialog
- Reject bid button with confirmation dialog
- Optional feedback field when rejecting bids

### 3. Administrator Bid Overview

#### 3.1 Comprehensive Bid Dashboard
- Admin-specific view showing all platform bids
- Advanced filtering options:
  - By provider
  - By customer
  - By service request
  - By status
  - By amount range
  - By date range
- Detailed data table with sortable columns

#### 3.2 Bid Statistics and Analytics
- Visual analytics dashboard showing:
  - Total bids, categorized by status
  - Conversion rates
  - Average bid amounts
  - Top providers by bid volume
  - Financial metrics
- Date range selector for all statistics

#### 3.3 Administrative Actions
- View complete bid details
- Export bid data to CSV/Excel
- Access to bid history and audit logs

## Technical Implementation

### API Integration
The frontend will integrate with the following backend endpoints:

1. **Provider Interfaces**
   - GET /api/service-requests - Browse available requests
   - POST /api/bid - Create new bid
   - GET /api/provider/bids - View provider's bids
   - PUT /api/bid/{id} - Update bid
   - DELETE /api/bid/{id} - Withdraw bid

2. **Customer Interfaces**
   - GET /api/service-requests/{serviceRequestId}/bids - View bids for a request
   - PATCH /api/bids/{bid}/status - Accept/reject bids

3. **Admin Interfaces**
   - GET /api/admin/bids - View all bids with advanced filtering
   - GET /api/admin/bid-stats - Access bid statistics

### UI Components
- Bid submission form
- Bid card/list item components
- Filter and sort controls
- Status indicators (color-coded)
- Action buttons with appropriate states
- Statistics visualizations (charts, graphs)
- Responsive tables for bid listings

### Notifications
- Real-time or push notifications for:
  - New bid received (for customers)
  - Bid status changes (for providers)
  - Approaching deadlines

## User Experience Considerations

### Provider Experience
- Streamlined bid creation process
- Clear visibility of bid statuses
- Easy tracking of all submitted bids
- Intuitive navigation between service requests and bids

### Customer Experience
- Simple comparison between multiple bids
- Clear action buttons for accepting/rejecting
- Visual indicators of bid quality/value
- Seamless flow from service request to bid management

### Admin Experience
- Comprehensive overview of platform bidding activity
- Easy access to detailed statistics
- Efficient filtering of large bid volumes
- Export capabilities for reporting

## Success Metrics
- Increase in bid submission rate
- Reduced time to bid response
- Higher bid acceptance rate
- Increased customer satisfaction with bid quality
- Improved provider engagement

## Future Enhancements (Phase 2)
- In-app messaging between customers and providers about bids
- Bid revision requests
- Counter-offer functionality
- Automated bid recommendations
- Provider reputation scoring based on bid success rate

## Implementation Timeline
Phase 1: Core bidding functionality for all user roles - 3 weeks
Phase 2: Enhanced analytics and reporting - 2 weeks
Phase 3: Optimizations based on user feedback - Ongoing


# Bid Management API Documentation

## Overview
The Bid Management API provides comprehensive functionality for managing bids in the JobON platform. It supports role-based access control for Customers, Providers, and Administrators with full CRUD operations, filtering, and analytics.

## Base URL
```
/api/bids
```

## Authentication
- **Method**: JWT Bearer Token
- **Header**: `Authorization: Bearer {token}`
- **Middleware**: `auth:api`

## API Endpoints

### 1. Get All Bids
**GET** `/api/bids`

**Role**: Admin, Customer, Provider
**Description**: Retrieve paginated list of bids with optional filtering

**Query Parameters**:
- `page` (integer, optional): Page number for pagination
- `per_page` (integer, optional): Items per page (default: 15)
- `status` (string, optional): Filter by status (requested, accepted, rejected)
- `service_request_id` (integer, optional): Filter by service request
- `provider_id` (integer, optional): Filter by provider

**Response Example**:
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "service_request_id": 5,
        "provider_id": 10,
        "amount": 150.00,
        "description": "I can complete this task efficiently",
        "status": "requested",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "service_request": {
          "id": 5,
          "title": "Website Development",
          "description": "Need a responsive website"
        },
        "provider": {
          "id": 10,
          "name": "John Doe",
          "email": "<EMAIL>"
        }
      }
    ],
    "current_page": 1,
    "per_page": 15,
    "total": 25
  },
  "message": "Bids retrieved successfully"
}
```

### 2. Create New Bid
**POST** `/api/bids`

**Role**: Provider
**Description**: Create a new bid for a service request

**Request Payload**:
```json
{
  "service_request_id": 5,
  "amount": 150.00,
  "description": "I can complete this task efficiently",
  "provider_id": 10
}
```

**Validation Rules**:
- `service_request_id`: Required, must exist in service_requests table
- `amount`: Required, numeric
- `description`: Optional, string
- `provider_id`: Optional, must exist in users table

**Response Example**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "service_request_id": 5,
    "provider_id": 10,
    "amount": 150.00,
    "description": "I can complete this task efficiently",
    "status": "requested",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "message": "Bid created successfully"
}
```

### 3. Update Bid
**PUT/PATCH** `/api/bids/{id}`

**Role**: Provider (own bids only)
**Description**: Update bid details

**Request Payload**:
```json
{
  "amount": 175.00,
  "description": "Updated description with more details"
}
```

**Response Example**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "amount": 175.00,
    "description": "Updated description with more details",
    "updated_at": "2024-01-15T11:00:00Z"
  },
  "message": "Bid updated successfully"
}
```

### 4. Delete Bid
**DELETE** `/api/bids/{id}`

**Role**: Provider (own bids only), Admin
**Description**: Soft delete a bid

**Response Example**:
```json
{
  "success": true,
  "message": "Bid deleted successfully"
}
```

### 5. Get Provider's Bids
**GET** `/api/bids/provider/{provider_id}`

**Role**: Provider (own bids), Admin
**Description**: Retrieve all bids for a specific provider

**Query Parameters**:
- `page` (integer, optional): Page number
- `per_page` (integer, optional): Items per page
- `status` (string, optional): Filter by status

**Response Example**:
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "service_request_id": 5,
        "amount": 150.00,
        "status": "accepted",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "current_page": 1,
    "total": 5
  },
  "message": "Provider bids retrieved successfully"
}
```

### 6. Get Service Request Bids
**GET** `/api/bids/service-request/{service_request_id}`

**Role**: Customer (own requests), Provider, Admin
**Description**: Retrieve all bids for a specific service request

**Query Parameters**:
- `page` (integer, optional): Page number
- `per_page` (integer, optional): Items per page
- `status` (string, optional): Filter by status

**Response Example**:
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "provider_id": 10,
        "amount": 150.00,
        "status": "requested",
        "provider": {
          "id": 10,
          "name": "John Doe",
          "rating": 4.5
        }
      }
    ],
    "current_page": 1,
    "total": 3
  },
  "message": "Service request bids retrieved successfully"
}
```

### 7. Update Bid Status
**PATCH** `/api/bids/{id}/status`

**Role**: Customer (for their service requests), Admin
**Description**: Accept or reject a bid

**Request Payload**:
```json
{
  "status": "accepted"
}
```

**Validation Rules**:
- `status`: Required, must be 'accepted' or 'rejected'

**Response Example**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "status": "accepted",
    "updated_at": "2024-01-15T12:00:00Z"
  },
  "message": "Bid status updated successfully"
}
```

### 8. Admin: Get All Bids
**GET** `/api/admin/bids`

**Role**: Admin only
**Description**: Administrative view of all bids with enhanced filtering

**Query Parameters**:
- `page` (integer, optional): Page number
- `per_page` (integer, optional): Items per page
- `status` (string, optional): Filter by status
- `date_from` (date, optional): Filter from date
- `date_to` (date, optional): Filter to date
- `provider_id` (integer, optional): Filter by provider
- `service_request_id` (integer, optional): Filter by service request

**Response Example**:
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "service_request_id": 5,
        "provider_id": 10,
        "amount": 150.00,
        "status": "accepted",
        "created_at": "2024-01-15T10:30:00Z",
        "service_request": {
          "title": "Website Development",
          "customer": {
            "name": "Jane Smith"
          }
        },
        "provider": {
          "name": "John Doe",
          "email": "<EMAIL>"
        }
      }
    ],
    "current_page": 1,
    "total": 150
  },
  "message": "Admin bids retrieved successfully"
}
```

### 9. Admin: Get Bid Statistics
**GET** `/api/admin/bids/stats`

**Role**: Admin only
**Description**: Retrieve bid analytics and statistics

**Query Parameters**:
- `period` (string, optional): 'today', 'week', 'month', 'year'
- `date_from` (date, optional): Custom date range start
- `date_to` (date, optional): Custom date range end

**Response Example**:
```json
{
  "success": true,
  "data": {
    "total_bids": 1250,
    "requested_bids": 450,
    "accepted_bids": 600,
    "rejected_bids": 200,
    "average_bid_amount": 175.50,
    "total_bid_value": 219375.00,
    "acceptance_rate": 75.0,
    "period_stats": {
      "period": "month",
      "new_bids": 85,
      "accepted_this_period": 64,
      "rejected_this_period": 15
    }
  },
  "message": "Bid statistics retrieved successfully"
}
```

## API Flow Diagram

```mermaid
sequenceDiagram
    participant C as Customer
    participant P as Provider
    participant API as Bid API
    participant DB as Database
    participant N as Notification
    participant A as Admin

    Note over C,A: Bid Creation Flow
    C->>API: POST /service-requests (Create service request)
    API->>DB: Store service request
    P->>API: GET /service-requests (Browse available requests)
    API->>DB: Fetch service requests
    API-->>P: Return service requests
    P->>API: POST /bids (Create bid)
    API->>DB: Store bid with status 'requested'
    API->>N: Trigger bid notification to customer
    API-->>P: Return bid confirmation

    Note over C,A: Bid Management Flow
    C->>API: GET /bids/service-request/{id} (View bids for their request)
    API->>DB: Fetch bids for service request
    API-->>C: Return bids list
    C->>API: PATCH /bids/{id}/status (Accept/Reject bid)
    API->>DB: Update bid status
    API->>N: Notify provider of status change
    API-->>C: Return status update confirmation

    Note over C,A: Provider Bid Tracking
    P->>API: GET /bids/provider/{id} (View own bids)
    API->>DB: Fetch provider's bids
    API-->>P: Return bid history
    P->>API: PUT /bids/{id} (Update bid details)
    API->>DB: Update bid information
    API-->>P: Return update confirmation

    Note over C,A: Admin Analytics
    A->>API: GET /admin/bids (View all bids)
    API->>DB: Fetch all bids with filters
    API-->>A: Return comprehensive bid data
    A->>API: GET /admin/bids/stats (Get analytics)
    API->>DB: Calculate bid statistics
    API-->>A: Return analytics dashboard data
```

## Error Responses

### Common Error Codes

**400 Bad Request**
```json
{
  "success": false,
  "message": "Invalid request parameters",
  "errors": {
    "amount": ["The amount field is required."]
  }
}
```

**401 Unauthorized**
```json
{
  "success": false,
  "message": "Unauthenticated"
}
```

**403 Forbidden**
```json
{
  "success": false,
  "message": "You don't have permission to access this resource"
}
```

**404 Not Found**
```json
{
  "success": false,
  "message": "Bid not found"
}
```

**422 Unprocessable Entity**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "service_request_id": ["The selected service request id is invalid."]
  }
}
```

## Status Codes Summary
- **200**: Success (GET, PUT, PATCH)
- **201**: Created (POST)
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **422**: Validation Error
- **500**: Internal Server Error

## Additional Notes

### Data Formats
- **Dates**: ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ)
- **Currency**: Decimal format (e.g., 150.00)
- **Pagination**: Standard Laravel pagination format

### Pagination
All list endpoints support pagination with the following parameters:
- `page`: Current page number (default: 1)
- `per_page`: Items per page (default: 15, max: 100)

### Soft Deletes
Bids use soft deletes, meaning deleted bids are not permanently removed but marked as deleted with a `deleted_at` timestamp.

### Role-Based Access Control
- **Customers**: Can view bids for their service requests and update bid statuses
- **Providers**: Can create, view, and update their own bids
- **Admins**: Full access to all bid operations and analytics

### Notifications
The system automatically sends notifications when:
- A new bid is created (notifies customer)
- A bid status is updated (notifies provider)
- A bid is accepted/rejected (notifies relevant parties)

### Rate Limiting
API endpoints are rate-limited to prevent abuse. Standard limits apply unless otherwise specified.