# Task ID: 8
# Title: Provider: Implement Bid Update/Withdraw Actions
# Status: pending
# Dependencies: 6
# Priority: high
# Description: Implement the functionality for providers to update the amount and description of their pending bids and to withdraw pending bids.
# Details:
Add 'Update' and 'Withdraw' action buttons to bid items on the provider dashboard. Enable these buttons only for bids with 'requested' status. For 'Update', implement a modal or form pre-filled with current bid details, allowing modification of amount and description, then send a `PUT/PATCH` request to `/api/bids/{id}`. For 'Withdraw', implement a confirmation dialog, then send a `DELETE` request to `/api/bids/{id}`. Handle success and error responses, updating the UI accordingly (e.g., removing withdrawn bid, refreshing list).

**API Interaction Details:**

**A. Update Bid:**
1.  **Endpoint & Method:**
    *   `PUT /api/bids/{id}` or `PATCH /api/bids/{id}`
        *   `{id}`: The ID of the bid to update.
2.  **Authentication:**
    *   Provider JWT token required in Authorization header.
3.  **Request Payload (`Content-Type: application/json`):**
    *   ```json
        {
          "amount": 160.00, // number, required
          "currency": "USD", // string, required if changed or system default not used
          "description": "Updated proposal details with more information.", // string, optional
          "estimatedDuration": "Updated duration" // string, optional
        }
        ```
4.  **Response Formats:**
    *   **Success (200 OK):**
        ```json
        {
          "success": true,
          "data": {
            "id": "bid_uuid",
            "serviceRequestId": "sr_uuid",
            "providerId": "provider_uuid",
            "amount": 160.00,
            "currency": "USD",
            "description": "Updated proposal details with more information.",
            "estimatedDuration": "Updated duration",
            "submissionDate": "2023-10-26T10:00:00Z",
            "status": "requested",
            "updatedAt": "2023-10-27T11:00:00Z"
          },
          "message": "Bid updated successfully."
        }
        ```
    *   **Error (e.g., 400, 401, 403, 404, 500):**
        ```json
        {
          "success": false,
          "error": {
            "code": "BID_UPDATE_FAILED",
            "message": "Failed to update bid.",
            "details": { /* field-specific validation errors */ }
          }
        }
        ```
5.  **Key Status Codes & Scenarios:**
    *   `200 OK`: Bid updated successfully.
    *   `400 Bad Request`: Validation error (e.g., invalid amount, missing required fields).
    *   `401 Unauthorized`: Invalid or missing provider token.
    *   `403 Forbidden`: Provider does not own the bid, or bid is not in 'requested' status.
    *   `404 Not Found`: Bid ID not found.
    *   `500 Internal Server Error`: Server-side issue.

**B. Withdraw Bid:**
1.  **Endpoint & Method:**
    *   `DELETE /api/bids/{id}`
        *   `{id}`: The ID of the bid to withdraw.
2.  **Authentication:**
    *   Provider JWT token required in Authorization header.
3.  **Request Payload:**
    *   N/A (DELETE request)
4.  **Response Formats:**
    *   **Success (200 OK or 204 No Content):**
        *   If 200 OK:
            ```json
            {
              "success": true,
              "message": "Bid withdrawn successfully.",
              "data": {
                "bidId": "bid_uuid",
                "newStatus": "withdrawn"
              }
            }
            ```
        *   If 204 No Content: No response body.
    *   **Error (e.g., 401, 403, 404, 500):**
        ```json
        {
          "success": false,
          "error": {
            "code": "BID_WITHDRAW_FAILED",
            "message": "Failed to withdraw bid."
          }
        }
        ```
5.  **Key Status Codes & Scenarios:**
    *   `200 OK` or `204 No Content`: Bid withdrawn successfully.
    *   `401 Unauthorized`: Invalid or missing provider token.
    *   `403 Forbidden`: Provider does not own the bid, or bid is not in 'requested' status.
    *   `404 Not Found`: Bid ID not found.
    *   `500 Internal Server Error`: Server-side issue.

# Test Strategy:
Test updating a pending bid with valid data, ensuring correct API payload and response. Test withdrawing a pending bid. Verify actions are disabled for accepted/rejected bids. Check API calls, request payloads, response formats, and UI updates for success and error cases, including different status codes.
