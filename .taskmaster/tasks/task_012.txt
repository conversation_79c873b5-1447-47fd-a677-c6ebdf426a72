# Task ID: 12
# Title: Customer: Implement Bid Accept/Reject Actions
# Status: pending
# Dependencies: 10
# Priority: high
# Description: Implement the functionality for customers to accept or reject bids received for their service requests.
# Details:
Add 'Accept' and 'Reject' action buttons to each bid item on the service request detail page. Implement confirmation dialogs for both actions. On confirmation, send a `PATCH` request to `/api/bids/{id}/status` with the new status (`'accepted'` or `'rejected'`). Handle success and error responses. Update the UI to reflect the new bid status and potentially disable actions on other bids for the same request if one is accepted.

**API Interaction Details:**

1.  **Endpoint & Method:**
    *   `PATCH /api/bids/{id}/status`
        *   `{id}`: The ID of the bid to update its status.
2.  **Authentication:**
    *   Customer JWT token required in Authorization header. (Customer must own the service request associated with the bid).
3.  **Request Payload (`Content-Type: application/json`):**
    *   ```json
        {
          "status": "accepted" // string, required. Can be "accepted" or "rejected".
        }
        ```
4.  **Response Formats:**
    *   **Success (200 OK):**
        ```json
        {
          "success": true,
          "data": {
            "id": "bid_uuid",
            "serviceRequestId": "sr_uuid",
            "providerId": "provider_uuid",
            "amount": 200.00,
            "status": "accepted", // The new status
            "updatedAt": "2023-10-27T12:00:00Z"
          },
          "message": "Bid status updated successfully."
        }
        ```
    *   **Error (e.g., 400, 401, 403, 404, 409, 500):**
        ```json
        {
          "success": false,
          "error": {
            "code": "BID_STATUS_UPDATE_FAILED",
            "message": "Failed to update bid status.",
            "details": { /* e.g., "Invalid status value", "Bid already processed" */ }
          }
        }
        ```
5.  **Key Status Codes & Scenarios:**
    *   `200 OK`: Bid status updated successfully.
    *   `400 Bad Request`: Invalid status value, or trying to update status of an already finalized bid.
    *   `401 Unauthorized`: Invalid or missing customer token.
    *   `403 Forbidden`: Customer does not own the service request associated with the bid, or trying to perform an invalid status transition.
    *   `404 Not Found`: Bid ID not found.
    *   `409 Conflict`: (Optional) If trying to accept a bid when another is already accepted for the same service request.
    *   `500 Internal Server Error`: Server-side issue.

# Test Strategy:
Test accepting a bid and rejecting a bid. Verify confirmation dialogs appear. Check API calls (payload, method, endpoint), response formats, and UI updates for success and error cases. Ensure actions are disabled appropriately after a bid is accepted/rejected. Test different status codes and error scenarios.
