# Task ID: 5
# Title: Provider: Bid Dashboard Page Layout
# Status: done
# Dependencies: None
# Priority: medium
# Description: Design and implement the basic layout for the provider's dashboard where they can view all their submitted bids.
# Details:
Create a new page component (e.g., `/provider/dashboard/bids`). Set up the basic page structure including space for filters, sorting controls, and a list/table to display bids. Use the chosen UI library for layout components.

# Test Strategy:
Verify the page renders with the correct layout structure.
