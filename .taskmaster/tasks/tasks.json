{"tasks": [{"id": 4, "title": "Provider: Bid Creation Form and Logic", "description": "Develop the UI form for providers to create and submit a new bid on a service request. This includes fields for bid amount and description.", "details": "Create a form component (e.g., `BidCreationForm`). Include input fields for 'Bid amount' (numeric, currency input) and 'Detailed description' (textarea). Use a form library like `react-hook-form` for form state management and validation (e.g., amount is numeric, description length). On submission, call the API client to send a `POST` request to `/api/bids` with the form data (`service_request_id`, `amount`, `description`, `provider_id`). Handle success (show confirmation notification) and validation errors (display error messages next to fields).", "testStrategy": "Test form submission with valid and invalid data. Verify frontend validation messages appear correctly. Confirm successful bid creation via API call and check the response. Test error handling for API validation errors (422).", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 5, "title": "Provider: Bid Dashboard Page Layout", "description": "Design and implement the basic layout for the provider's dashboard where they can view all their submitted bids.", "details": "Create a new page component (e.g., `/provider/dashboard/bids`). Set up the basic page structure including space for filters, sorting controls, and a list/table to display bids. Use the chosen UI library for layout components.", "testStrategy": "Verify the page renders with the correct layout structure.", "priority": "medium", "dependencies": [], "status": "done", "subtasks": []}, {"id": 6, "title": "Provider: Fetch and Display Provider Bids", "description": "Implement the logic to fetch and display the list of bids submitted by the currently logged-in provider on their dashboard.", "status": "pending", "dependencies": [5], "priority": "high", "details": "In the provider bid dashboard component, use React Query to fetch data from `GET /api/bids/provider/{provider_id}` (replace `{provider_id}` with the logged-in user's ID). Display the fetched bids in a list or table component. Each item should show Service Request Title, Bid Amount, Submission Date, and Status. Use visual indicators (e.g., color-coded badges) for status.\n\n**API Interaction Details:**\n\n1.  **Endpoint & Method:**\n    *   `GET /api/bids/provider/{provider_id}`\n        *   `{provider_id}`: The ID of the currently logged-in provider.\n2.  **Authentication:**\n    *   Provider JWT token required in Authorization header.\n3.  **Request Payload:**\n    *   N/A (GET request)\n4.  **Query Parameters (Optional for pagination, filtering, sorting - see Task 7):**\n    *   `page`: number (e.g., 1, 2, ... for pagination)\n    *   `limit`: number (e.g., 10, 20 items per page)\n5.  **Response Formats:**\n    *   **Success (200 OK):**\n        ```json\n        {\n          \"success\": true,\n          \"data\": {\n            \"bids\": [\n              {\n                \"id\": \"bid_uuid\",\n                \"serviceRequestId\": \"sr_uuid\",\n                \"serviceRequestTitle\": \"Title of the Service Request\",\n                \"amount\": 150.00,\n                \"currency\": \"USD\",\n                \"submissionDate\": \"2023-10-26T10:00:00Z\",\n                \"status\": \"requested\" // \"requested\", \"accepted\", \"rejected\", \"withdrawn\"\n              }\n            ],\n            \"pagination\": {\n              \"currentPage\": 1,\n              \"totalPages\": 5,\n              \"totalItems\": 48,\n              \"itemsPerPage\": 10\n            }\n          },\n          \"message\": \"Provider bids fetched successfully.\"\n        }\n        ```\n    *   **Error (e.g., 400, 401, 403, 404, 500):**\n        ```json\n        {\n          \"success\": false,\n          \"error\": {\n            \"code\": \"FETCH_PROVIDER_BIDS_FAILED\",\n            \"message\": \"Failed to fetch provider bids.\",\n            \"details\": { /* optional additional error details */ }\n          }\n        }\n        ```\n6.  **Key Status Codes & Scenarios:**\n    *   `200 OK`: Bids fetched successfully.\n    *   `401 Unauthorized`: Invalid or missing provider token.\n    *   `403 Forbidden`: User is not a provider or trying to access another provider's bids.\n    *   `404 Not Found`: Provider ID not found.\n    *   `500 Internal Server Error`: Server-side issue.", "testStrategy": "Verify the dashboard loads and displays the provider's bids fetched from the API. Check that all required bid details are shown correctly. Test pagination if the API returns paginated data. Verify API response structure and error handling.", "subtasks": []}, {"id": 7, "title": "Provider: Implement Provider Bid Filtering and Sorting", "description": "Add filtering and sorting controls to the provider's bid dashboard, allowing them to organize their bids.", "status": "pending", "dependencies": [6], "priority": "medium", "details": "Add UI components for filtering by status (requested, accepted, rejected), date range, and service request. Add controls for sorting by amount, date, and status. Update the React Query fetch call (`GET /api/bids/provider/{provider_id}`) to include the selected filter and sort parameters as query parameters (`status`, `date_from`, `date_to`, `service_request_id`, `sort_by`, `sort_order`). Ensure the UI updates when filters/sort order change.\n\n**API Interaction Details (for `GET /api/bids/provider/{provider_id}` with filtering/sorting):**\n\n1.  **Endpoint & Method:**\n    *   `GET /api/bids/provider/{provider_id}`\n        *   `{provider_id}`: The ID of the currently logged-in provider.\n2.  **Authentication:**\n    *   Provider JWT token required in Authorization header.\n3.  **Request Payload:**\n    *   N/A (GET request)\n4.  **Query Parameters:**\n    *   `status`: string (e.g., \"requested\", \"accepted\", \"rejected\", \"withdrawn\" - can be comma-separated for multiple values if API supports, e.g., \"requested,accepted\")\n    *   `date_from`: string (ISO 8601 date, e.g., \"2023-01-01\")\n    *   `date_to`: string (ISO 8601 date, e.g., \"2023-12-31\")\n    *   `service_request_id`: string (UUID of a specific service request to filter by)\n    *   `sort_by`: string (e.g., \"amount\", \"submissionDate\", \"status\")\n    *   `sort_order`: string (\"asc\" or \"desc\")\n    *   `page`: number (for pagination)\n    *   `limit`: number (items per page for pagination)\n5.  **Response Formats:**\n    *   **Success (200 OK):**\n        ```json\n        {\n          \"success\": true,\n          \"data\": {\n            \"bids\": [ /* filtered and sorted list of bids */ ],\n            \"pagination\": { /* pagination details */ }\n          },\n          \"message\": \"Provider bids fetched successfully with filters/sorting.\"\n        }\n        ```\n    *   **Error (e.g., 400, 401, 403, 500):**\n        ```json\n        {\n          \"success\": false,\n          \"error\": {\n            \"code\": \"INVALID_FILTER_PARAMS\",\n            \"message\": \"Invalid filter or sort parameters provided.\",\n            \"details\": { /* specific field errors */ }\n          }\n        }\n        ```\n6.  **Key Status Codes & Scenarios:**\n    *   `200 OK`: Bids fetched successfully (filtered/sorted).\n    *   `400 Bad Request`: Invalid filter/sort parameter values or format.\n    *   `401 Unauthorized`: Invalid or missing provider token.\n    *   `403 Forbidden`: User is not a provider.\n    *   `500 Internal Server Error`: Server-side issue.", "testStrategy": "Test each filter and sort option to ensure the displayed bid list updates correctly according to the selected criteria and matches API responses. Verify API query parameters are correctly sent and responses are handled.", "subtasks": []}, {"id": 8, "title": "Provider: Implement Bid Update/Withdraw Actions", "description": "Implement the functionality for providers to update the amount and description of their pending bids and to withdraw pending bids.", "status": "pending", "dependencies": [6], "priority": "high", "details": "Add 'Update' and 'Withdraw' action buttons to bid items on the provider dashboard. Enable these buttons only for bids with 'requested' status. For 'Update', implement a modal or form pre-filled with current bid details, allowing modification of amount and description, then send a `PUT/PATCH` request to `/api/bids/{id}`. For 'Withdraw', implement a confirmation dialog, then send a `DELETE` request to `/api/bids/{id}`. Handle success and error responses, updating the UI accordingly (e.g., removing withdrawn bid, refreshing list).\n\n**API Interaction Details:**\n\n**A. Update Bid:**\n1.  **Endpoint & Method:**\n    *   `PUT /api/bids/{id}` or `PATCH /api/bids/{id}`\n        *   `{id}`: The ID of the bid to update.\n2.  **Authentication:**\n    *   Provider JWT token required in Authorization header.\n3.  **Request Payload (`Content-Type: application/json`):**\n    *   ```json\n        {\n          \"amount\": 160.00, // number, required\n          \"currency\": \"USD\", // string, required if changed or system default not used\n          \"description\": \"Updated proposal details with more information.\", // string, optional\n          \"estimatedDuration\": \"Updated duration\" // string, optional\n        }\n        ```\n4.  **Response Formats:**\n    *   **Success (200 OK):**\n        ```json\n        {\n          \"success\": true,\n          \"data\": {\n            \"id\": \"bid_uuid\",\n            \"serviceRequestId\": \"sr_uuid\",\n            \"providerId\": \"provider_uuid\",\n            \"amount\": 160.00,\n            \"currency\": \"USD\",\n            \"description\": \"Updated proposal details with more information.\",\n            \"estimatedDuration\": \"Updated duration\",\n            \"submissionDate\": \"2023-10-26T10:00:00Z\",\n            \"status\": \"requested\",\n            \"updatedAt\": \"2023-10-27T11:00:00Z\"\n          },\n          \"message\": \"Bid updated successfully.\"\n        }\n        ```\n    *   **Error (e.g., 400, 401, 403, 404, 500):**\n        ```json\n        {\n          \"success\": false,\n          \"error\": {\n            \"code\": \"BID_UPDATE_FAILED\",\n            \"message\": \"Failed to update bid.\",\n            \"details\": { /* field-specific validation errors */ }\n          }\n        }\n        ```\n5.  **Key Status Codes & Scenarios:**\n    *   `200 OK`: Bid updated successfully.\n    *   `400 Bad Request`: Validation error (e.g., invalid amount, missing required fields).\n    *   `401 Unauthorized`: Invalid or missing provider token.\n    *   `403 Forbidden`: Provider does not own the bid, or bid is not in 'requested' status.\n    *   `404 Not Found`: Bid ID not found.\n    *   `500 Internal Server Error`: Server-side issue.\n\n**B. Withdraw Bid:**\n1.  **Endpoint & Method:**\n    *   `DELETE /api/bids/{id}`\n        *   `{id}`: The ID of the bid to withdraw.\n2.  **Authentication:**\n    *   Provider JWT token required in Authorization header.\n3.  **Request Payload:**\n    *   N/A (DELETE request)\n4.  **Response Formats:**\n    *   **Success (200 OK or 204 No Content):**\n        *   If 200 OK:\n            ```json\n            {\n              \"success\": true,\n              \"message\": \"Bid withdrawn successfully.\",\n              \"data\": {\n                \"bidId\": \"bid_uuid\",\n                \"newStatus\": \"withdrawn\"\n              }\n            }\n            ```\n        *   If 204 No Content: No response body.\n    *   **Error (e.g., 401, 403, 404, 500):**\n        ```json\n        {\n          \"success\": false,\n          \"error\": {\n            \"code\": \"BID_WITHDRAW_FAILED\",\n            \"message\": \"Failed to withdraw bid.\"\n          }\n        }\n        ```\n5.  **Key Status Codes & Scenarios:**\n    *   `200 OK` or `204 No Content`: Bid withdrawn successfully.\n    *   `401 Unauthorized`: Invalid or missing provider token.\n    *   `403 Forbidden`: Provider does not own the bid, or bid is not in 'requested' status.\n    *   `404 Not Found`: Bid ID not found.\n    *   `500 Internal Server Error`: Server-side issue.", "testStrategy": "Test updating a pending bid with valid data, ensuring correct API payload and response. Test withdrawing a pending bid. Verify actions are disabled for accepted/rejected bids. Check API calls, request payloads, response formats, and UI updates for success and error cases, including different status codes.", "subtasks": []}, {"id": 9, "title": "Customer: Service Request Detail Page Bid Section", "description": "Integrate the bid viewing interface into the service request detail page for customers, showing all bids received for that specific request.", "status": "pending", "dependencies": [], "priority": "medium", "details": "Modify the existing Service Request Detail page component (or create one if it doesn't exist). Add a dedicated section to display bids received for the request. This section should be visible only to the customer who owns the service request.", "testStrategy": "Verify the bid section appears correctly on the service request detail page for the customer who created the request.", "subtasks": []}, {"id": 10, "title": "Customer: Fetch and Display Service Request Bids", "description": "Implement fetching and displaying the list of bids associated with a specific service request on the customer's service request detail page.", "status": "pending", "dependencies": [9], "priority": "high", "details": "In the bid section of the Service Request Detail page, use React Query to fetch data from `GET /api/bids/service-request/{service_request_id}`. Display each bid, including Provider Name (with basic profile info if available), Bid Amount, Proposal Description, Submission Date, and Status indicators. Ensure provider rating/reviews are displayed if provided by the API.\n\n**API Interaction Details:**\n\n1.  **Endpoint & Method:**\n    *   `GET /api/bids/service-request/{service_request_id}`\n        *   `{service_request_id}`: The ID of the service request for which to fetch bids.\n2.  **Authentication:**\n    *   Customer JWT token required in Authorization header. (The customer must own the service request).\n3.  **Request Payload:**\n    *   N/A (GET request)\n4.  **Query Parameters (Optional for pagination, filtering, sorting - see Task 11):**\n    *   `page`: number (e.g., 1, 2, ... for pagination)\n    *   `limit`: number (e.g., 10, 20 items per page)\n5.  **Response Formats:**\n    *   **Success (200 OK):**\n        ```json\n        {\n          \"success\": true,\n          \"data\": {\n            \"bids\": [\n              {\n                \"id\": \"bid_uuid\",\n                \"providerId\": \"provider_uuid\",\n                \"providerName\": \"Awesome Provider Inc.\",\n                \"providerProfile\": {\n                  \"avatarUrl\": \"https://example.com/avatar.png\",\n                  \"tagline\": \"Quality work, on time.\",\n                  \"averageRating\": 4.8,\n                  \"totalReviews\": 120\n                },\n                \"amount\": 200.00,\n                \"currency\": \"USD\",\n                \"proposalDescription\": \"Detailed proposal for your service request...\",\n                \"submissionDate\": \"2023-10-25T14:30:00Z\",\n                \"status\": \"requested\"\n              }\n            ],\n            \"pagination\": {\n              \"currentPage\": 1,\n              \"totalPages\": 2,\n              \"totalItems\": 15,\n              \"itemsPerPage\": 10\n            }\n          },\n          \"message\": \"Bids for service request fetched successfully.\"\n        }\n        ```\n    *   **Error (e.g., 400, 401, 403, 404, 500):**\n        ```json\n        {\n          \"success\": false,\n          \"error\": {\n            \"code\": \"FETCH_SR_BIDS_FAILED\",\n            \"message\": \"Failed to fetch bids for the service request.\",\n            \"details\": { /* optional additional error details */ }\n          }\n        }\n        ```\n6.  **Key Status Codes & Scenarios:**\n    *   `200 OK`: Bids fetched successfully.\n    *   `401 Unauthorized`: Invalid or missing customer token.\n    *   `403 Forbidden`: Customer does not own the service request.\n    *   `404 Not Found`: Service request ID not found.\n    *   `500 Internal Server Error`: Server-side issue.", "testStrategy": "Verify the bid list loads and displays bids for the specific service request. Check that all required bid and provider details are shown correctly. Verify API response structure and error handling for various scenarios (e.g., no bids, unauthorized access).", "subtasks": []}, {"id": 11, "title": "Customer: Implement Customer Bid Sorting and Comparison UI", "description": "Add sorting options and visual comparison tools to help customers evaluate bids on their service requests.", "status": "pending", "dependencies": [10], "priority": "medium", "details": "Add UI controls to sort bids by amount and submission date. Implement logic to update the fetch call (`GET /api/bids/service-request/{service_request_id}`) with `sort_by` and `sort_order` query parameters. Add visual elements to compare bid amounts, potentially highlighting them against the original service request budget if available.\n\n**API Interaction Details (for `GET /api/bids/service-request/{service_request_id}` with sorting):**\n\n1.  **Endpoint & Method:**\n    *   `GET /api/bids/service-request/{service_request_id}`\n        *   `{service_request_id}`: The ID of the service request.\n2.  **Authentication:**\n    *   Customer JWT token required in Authorization header.\n3.  **Request Payload:**\n    *   N/A (GET request)\n4.  **Query Parameters:**\n    *   `sort_by`: string (e.g., \"amount\", \"submissionDate\", \"providerRating\")\n    *   `sort_order`: string (\"asc\" or \"desc\")\n    *   `page`: number (for pagination)\n    *   `limit`: number (items per page for pagination)\n5.  **Response Formats:**\n    *   **Success (200 OK):**\n        ```json\n        {\n          \"success\": true,\n          \"data\": {\n            \"bids\": [ /* sorted list of bids for this service request */ ],\n            \"pagination\": { /* pagination details */ }\n          },\n          \"message\": \"Bids for service request fetched successfully with sorting.\"\n        }\n        ```\n    *   **Error (e.g., 400, 401, 403, 404, 500):**\n        ```json\n        {\n          \"success\": false,\n          \"error\": {\n            \"code\": \"INVALID_SORT_PARAMS\",\n            \"message\": \"Invalid sort parameters provided.\",\n            \"details\": { /* specific field errors */ }\n          }\n        }\n        ```\n6.  **Key Status Codes & Scenarios:**\n    *   `200 OK`: Bids fetched successfully (sorted).\n    *   `400 Bad Request`: Invalid sort parameter values or format.\n    *   `401 Unauthorized`: Invalid or missing customer token.\n    *   `403 Forbidden`: Customer does not own the service request.\n    *   `404 Not Found`: Service request ID not found.\n    *   `500 Internal Server Error`: Server-side issue.", "testStrategy": "Test sorting by amount and date. Verify the bid list reorders correctly. Check that visual comparison elements (if implemented) are displayed as intended. Verify API query parameters for sorting are correctly sent and responses are handled.", "subtasks": []}, {"id": 12, "title": "Customer: Implement Bid Accept/Reject Actions", "description": "Implement the functionality for customers to accept or reject bids received for their service requests.", "status": "pending", "dependencies": [10], "priority": "high", "details": "Add 'Accept' and 'Reject' action buttons to each bid item on the service request detail page. Implement confirmation dialogs for both actions. On confirmation, send a `PATCH` request to `/api/bids/{id}/status` with the new status (`'accepted'` or `'rejected'`). Handle success and error responses. Update the UI to reflect the new bid status and potentially disable actions on other bids for the same request if one is accepted.\n\n**API Interaction Details:**\n\n1.  **Endpoint & Method:**\n    *   `PATCH /api/bids/{id}/status`\n        *   `{id}`: The ID of the bid to update its status.\n2.  **Authentication:**\n    *   Customer JWT token required in Authorization header. (Customer must own the service request associated with the bid).\n3.  **Request Payload (`Content-Type: application/json`):**\n    *   ```json\n        {\n          \"status\": \"accepted\" // string, required. Can be \"accepted\" or \"rejected\".\n        }\n        ```\n4.  **Response Formats:**\n    *   **Success (200 OK):**\n        ```json\n        {\n          \"success\": true,\n          \"data\": {\n            \"id\": \"bid_uuid\",\n            \"serviceRequestId\": \"sr_uuid\",\n            \"providerId\": \"provider_uuid\",\n            \"amount\": 200.00,\n            \"status\": \"accepted\", // The new status\n            \"updatedAt\": \"2023-10-27T12:00:00Z\"\n          },\n          \"message\": \"Bid status updated successfully.\"\n        }\n        ```\n    *   **Error (e.g., 400, 401, 403, 404, 409, 500):**\n        ```json\n        {\n          \"success\": false,\n          \"error\": {\n            \"code\": \"BID_STATUS_UPDATE_FAILED\",\n            \"message\": \"Failed to update bid status.\",\n            \"details\": { /* e.g., \"Invalid status value\", \"Bid already processed\" */ }\n          }\n        }\n        ```\n5.  **Key Status Codes & Scenarios:**\n    *   `200 OK`: Bid status updated successfully.\n    *   `400 Bad Request`: Invalid status value, or trying to update status of an already finalized bid.\n    *   `401 Unauthorized`: Invalid or missing customer token.\n    *   `403 Forbidden`: Customer does not own the service request associated with the bid, or trying to perform an invalid status transition.\n    *   `404 Not Found`: Bid ID not found.\n    *   `409 Conflict`: (Optional) If trying to accept a bid when another is already accepted for the same service request.\n    *   `500 Internal Server Error`: Server-side issue.", "testStrategy": "Test accepting a bid and rejecting a bid. Verify confirmation dialogs appear. Check API calls (payload, method, endpoint), response formats, and UI updates for success and error cases. Ensure actions are disabled appropriately after a bid is accepted/rejected. Test different status codes and error scenarios.", "subtasks": []}, {"id": 13, "title": "Admin: Admin Dashboard Layout and Navigation", "description": "Design and implement the basic layout and navigation for the administrator's bid overview dashboard.", "status": "pending", "dependencies": [], "priority": "medium", "details": "Create a new page component (e.g., `/admin/dashboard/bids`). Set up the page structure, including navigation specific to admin features (if applicable) and areas for displaying the comprehensive bid table and statistics.", "testStrategy": "Verify the admin bid dashboard page loads with the correct layout and navigation elements.", "subtasks": [{"id": 1, "title": "Create Admin Dashboard Page Component and Route", "description": "Set up the initial React/TypeScript component for the admin dashboard (e.g., `AdminBidsDashboardPage.tsx`) and configure its route (e.g., `/admin/dashboard/bids`) within the application's routing system.", "dependencies": [], "details": "Create a new file for the page component under the appropriate admin directory structure (e.g., `src/pages/admin/dashboard/bids/AdminBidsDashboardPage.tsx`). Register the route in the main router configuration (e.g., `AppRoutes.tsx`), ensuring it's protected by admin authentication middleware if applicable. The initial component should render a basic placeholder text like 'Admin Bids Dashboard Page'.", "status": "pending", "testStrategy": "Verify the route `/admin/dashboard/bids` loads the new page component. Check browser console for any routing errors. Ensure admin authentication (if implemented) correctly gates access to this route."}, {"id": 2, "title": "Design and Implement Basic Admin Layout Structure Component", "description": "Create a reusable `AdminLayout.tsx` component that defines the main structural areas: a header, a sidebar (or top navigation for admin), and a main content area. This layout will wrap the admin dashboard page and other future admin pages.", "dependencies": [1], "details": "Develop a new `AdminLayout.tsx` component (e.g., in `src/components/admin/layout/AdminLayout.tsx`). Use semantic HTML elements (e.g., `<header>`, `<aside>`, `<main>`). Initially, these areas can be simple `div`s with distinct background colors or borders for visualization. Ensure it follows the established component-based architecture and can accept children props for content.", "status": "pending", "testStrategy": "Render the `AdminBidsDashboardPage` wrapped in the `AdminLayout`. Visually inspect that the header, sidebar/nav, and main content areas are distinct and occupy their intended positions on the page. Check that the page content (placeholder from subtask 1) is rendered within the main content area."}, {"id": 3, "title": "Develop Admin-Specific Navigation Component", "description": "Create a dedicated `AdminNavigation.tsx` component (e.g., `AdminSidebarNav.tsx` or `AdminHeaderNav.tsx`) containing links relevant to admin functionalities. Initially, include a link to 'Bid Overview' and placeholders for future admin sections.", "dependencies": [], "details": "This component will house navigation items like 'Dashboard (Bids)', 'User Management', 'Settings', etc. Use React Router's `NavLink` components for navigation to allow active state styling. For now, focus on the structure and a few sample links. Style with basic Tailwind CSS for clarity and interactivity (hover states).", "status": "pending", "testStrategy": "Render the `AdminNavigation` component in isolation (e.g., using Storybook or a temporary test page). Verify links are present, correctly point to intended (even if not yet created) routes, and basic hover/active styles work. Check for accessibility attributes."}, {"id": 4, "title": "Integrate Admin Navigation into Admin Layout", "description": "Incorporate the `AdminNavigation` component into the `AdminLayout` component, placing it in the designated sidebar or header area.", "dependencies": [2, 3], "details": "Import `AdminNavigation` into `AdminLayout.tsx` and render it within the appropriate layout section (e.g., the `<aside>` for a sidebar or `<header>` for top navigation). Ensure props are passed correctly if needed (e.g., for toggling visibility on mobile).", "status": "pending", "testStrategy": "View the `/admin/dashboard/bids` page. Verify the admin navigation is now visible within the overall admin layout and is positioned correctly. Test that navigation links within the integrated component are functional (at least for the 'Bid Overview' link)."}, {"id": 5, "title": "Define Placeholder Regions for Bid Table and Statistics in Page Component", "description": "Within the main content area of the `AdminBidsDashboardPage` component, define and style distinct placeholder regions where the comprehensive bid table and bid statistics will eventually be displayed.", "dependencies": [1, 2], "details": "Inside `AdminBidsDashboardPage.tsx`, use `div` elements with clear IDs or class names (e.g., `bid-table-placeholder`, `statistics-placeholder`). Add temporary styling (e.g., borders, background colors, placeholder text like 'Comprehensive Bid Table Area', 'Key Statistics Area') to demarcate these sections. Use Tailwind CSS for basic layout (e.g., flexbox or grid) of these placeholders within the main content area provided by `AdminLayout`.", "status": "pending", "testStrategy": "Navigate to `/admin/dashboard/bids`. Visually confirm that the main content area (inside the `AdminLayout`) shows clearly marked sections for the bid table and statistics, arranged according to the intended design."}, {"id": 6, "title": "Implement Responsive Design for Core Layout and Navigation", "description": "Apply Tailwind CSS responsive prefixes (e.g., `sm:`, `md:`, `lg:`) to ensure the `AdminLayout` and `AdminNavigation` components adapt gracefully to different screen sizes (desktop, tablet, mobile).", "dependencies": [4], "details": "Consider how the sidebar might collapse into a hamburger menu on smaller screens, or how header navigation items might stack or hide. Adjust widths, margins, paddings, font sizes, and display properties (e.g., `hidden md:block`) responsively. Implement toggle functionality for mobile navigation if a collapsible sidebar is chosen.", "status": "pending", "testStrategy": "Use browser developer tools to simulate various screen sizes (e.g., 360px, 768px, 1024px, 1440px). Verify the layout and navigation remain usable and visually appealing. Check for content overflow, broken elements, and proper functioning of mobile navigation toggles."}, {"id": 7, "title": "Apply Consistent Tailwind CSS Styling based on JobON.app Patterns", "description": "Refine the styling of the `AdminLayout`, `AdminNavigation`, and placeholder regions using Tailwind CSS, adhering to the JobON.app's existing design system (colors, fonts, spacing) and utility class patterns for a consistent look and feel.", "dependencies": [5, 6], "details": "Replace temporary styling with project-consistent colors (e.g., `bg-primary`, `text-neutral-800`), fonts, spacing utilities (e.g., `p-4`, `m-2`), and component styles. Ensure class names are semantic and follow Tailwind best practices. Refer to existing JobON.app components for styling guidance to maintain visual consistency.", "status": "pending", "testStrategy": "Visually inspect the admin dashboard page for consistency with other parts of the JobON.app. Check for correct application of theme colors, fonts, and spacing. Ensure no custom CSS is introduced unless absolutely necessary and documented. Validate against any existing style guides."}, {"id": 8, "title": "Code Review and Refactor for Adherence to JobON.app Standards", "description": "Review all newly created components (`AdminBidsDashboardPage.tsx`, `AdminLayout.tsx`, `AdminNavigation.tsx`) for adherence to JobON.app's established React/TypeScript structure, component-based architecture, naming conventions, and code quality standards. Refactor as necessary.", "dependencies": [7], "details": "Check for proper type definitions (TypeScript), component modularity, reusability, clear prop interfaces, and efficient state management (if any at this stage, e.g., for mobile nav toggle). Ensure file organization aligns with project standards. Remove any commented-out code, console logs, or unused imports. Run linters and formatters.", "status": "pending", "testStrategy": "Perform a self-review or facilitate a peer review of the codebase. Use static analysis tools (linters like ESLint, formatters like Prettier) configured for the project. Manually check against a checklist of JobON.app coding guidelines, patterns, and best practices for React/TypeScript development."}]}, {"id": 14, "title": "Admin: Comprehensive Bid Listing (<PERSON><PERSON>, <PERSON><PERSON>lay, <PERSON>lter, Sort)", "description": "Implement fetching, displaying, filtering, and sorting for the comprehensive list of all bids accessible to administrators.", "status": "pending", "dependencies": [13], "priority": "high", "details": "In the admin bid dashboard, use React Query to fetch data from `GET /api/admin/bids`. Display the data in a responsive table component (e.g., using Material UI DataGrid or a custom table). Implement advanced filtering UI controls for provider, customer, service request, status, amount range, and date range. Add sorting functionality for table columns. Update the fetch call with corresponding query parameters (`provider_id`, `customer_id`, `service_request_id`, `status`, `date_from`, `date_to`, `amount_min`, `amount_max`, `sort_by`, `sort_order`, `search_term`). Implement pagination based on API response.\n\n**API Interaction Details:**\n\n1.  **Endpoint & Method:**\n    *   `GET /api/admin/bids`\n2.  **Authentication:**\n    *   Admin JWT token required in Authorization header.\n3.  **Request Payload:**\n    *   N/A (GET request)\n4.  **Query Parameters:**\n    *   `provider_id`: string (UUID of a provider to filter by)\n    *   `customer_id`: string (UUID of a customer to filter by)\n    *   `service_request_id`: string (UUID of a service request to filter by)\n    *   `status`: string (e.g., \"requested\", \"accepted\", \"rejected\", \"withdrawn\", \"completed\" - comma-separated for multiple values if API supports)\n    *   `date_from`: string (ISO 8601 date, e.g., \"2023-01-01\")\n    *   `date_to`: string (ISO 8601 date, e.g., \"2023-12-31\")\n    *   `amount_min`: number (minimum bid amount)\n    *   `amount_max`: number (maximum bid amount)\n    *   `sort_by`: string (e.g., \"submissionDate\", \"amount\", \"status\", \"providerName\", \"customerName\")\n    *   `sort_order`: string (\"asc\" or \"desc\")\n    *   `page`: number (for pagination)\n    *   `limit`: number (items per page for pagination)\n    *   `search_term`: string (for general text search across relevant fields)\n5.  **Response Formats:**\n    *   **Success (200 OK):**\n        ```json\n        {\n          \"success\": true,\n          \"data\": {\n            \"bids\": [\n              {\n                \"id\": \"bid_uuid\",\n                \"serviceRequestId\": \"sr_uuid\",\n                \"serviceRequestTitle\": \"Title of Service Request\",\n                \"providerId\": \"provider_uuid\",\n                \"providerName\": \"Provider X\",\n                \"customerId\": \"customer_uuid\",\n                \"customerName\": \"Customer Y\",\n                \"amount\": 100.00,\n                \"currency\": \"USD\",\n                \"submissionDate\": \"2023-10-20T09:00:00Z\",\n                \"status\": \"accepted\",\n                \"updatedAt\": \"2023-10-21T10:00:00Z\"\n              }\n            ],\n            \"pagination\": {\n              \"currentPage\": 1,\n              \"totalPages\": 10,\n              \"totalItems\": 95,\n              \"itemsPerPage\": 10\n            }\n          },\n          \"message\": \"Admin bids fetched successfully.\"\n        }\n        ```\n    *   **Error (e.g., 400, 401, 403, 500):**\n        ```json\n        {\n          \"success\": false,\n          \"error\": {\n            \"code\": \"FETCH_ADMIN_BIDS_FAILED\",\n            \"message\": \"Failed to fetch admin bids.\",\n            \"details\": { /* e.g., \"Invalid filter parameter\" */ }\n          }\n        }\n        ```\n6.  **Key Status Codes & Scenarios:**\n    *   `200 OK`: Bids fetched successfully.\n    *   `400 Bad Request`: Invalid filter/sort parameter values or format.\n    *   `401 Unauthorized`: Invalid or missing admin token.\n    *   `403 Forbidden`: User is not an admin.\n    *   `500 Internal Server Error`: Server-side issue.", "testStrategy": "Verify the table loads and displays all bids. Test each filtering option individually and in combination. Test sorting on different columns. Verify pagination works correctly. Check loading and error states. Validate API request (query params) and response structures, including error handling.", "subtasks": []}, {"id": 15, "title": "Admin: Bid Statistics and Analytics (Fetch, Display Charts)", "description": "Implement fetching and displaying bid statistics and analytics data for administrators using charts and visualizations.", "status": "pending", "dependencies": [13], "priority": "high", "details": "In the admin bid dashboard, use React Query to fetch data from `GET /api/admin/bids/stats`. Use a charting library like `chart.js` or `recharts` to visualize the statistics (e.g., total bids by status, conversion rates, average bid amounts). Add a date range selector UI that updates the fetch call with `period`, `date_from`, and `date_to` query parameters. Display key metrics as summary numbers.\n\n**API Interaction Details:**\n\n1.  **Endpoint & Method:**\n    *   `GET /api/admin/bids/stats`\n2.  **Authentication:**\n    *   Admin JWT token required in Authorization header.\n3.  **Request Payload:**\n    *   N/A (GET request)\n4.  **Query Parameters:**\n    *   `period`: string (e.g., \"daily\", \"weekly\", \"monthly\", \"yearly\", \"custom\" - if 'custom', `date_from` and `date_to` are required)\n    *   `date_from`: string (ISO 8601 date, e.g., \"2023-01-01\" - required if `period` is \"custom\")\n    *   `date_to`: string (ISO 8601 date, e.g., \"2023-12-31\" - required if `period` is \"custom\")\n    *   `group_by`: string (optional, e.g., \"status\", \"category\" - to get breakdowns)\n5.  **Response Formats:**\n    *   **Success (200 OK):**\n        ```json\n        {\n          \"success\": true,\n          \"data\": {\n            \"summary\": {\n              \"totalBids\": 1250,\n              \"totalValueBids\": 250000.00,\n              \"averageBidAmount\": 200.00,\n              \"acceptedBids\": 300,\n              \"acceptanceRate\": 0.24\n            },\n            \"bidsByStatus\": [\n              { \"status\": \"requested\", \"count\": 700, \"value\": 140000.00 },\n              { \"status\": \"accepted\", \"count\": 300, \"value\": 75000.00 },\n              { \"status\": \"rejected\", \"count\": 200, \"value\": 30000.00 },\n              { \"status\": \"withdrawn\", \"count\": 50, \"value\": 5000.00 }\n            ],\n            \"bidTrends\": [\n              { \"date\": \"2023-10-01\", \"count\": 50, \"averageAmount\": 190.00 },\n              { \"date\": \"2023-10-02\", \"count\": 55, \"averageAmount\": 210.00 }\n            ]\n          },\n          \"message\": \"Bid statistics fetched successfully.\"\n        }\n        ```\n    *   **Error (e.g., 400, 401, 403, 500):**\n        ```json\n        {\n          \"success\": false,\n          \"error\": {\n            \"code\": \"FETCH_BID_STATS_FAILED\",\n            \"message\": \"Failed to fetch bid statistics.\",\n            \"details\": { /* e.g., \"Invalid period parameter\" */ }\n          }\n        }\n        ```\n6.  **Key Status Codes & Scenarios:**\n    *   `200 OK`: Statistics fetched successfully.\n    *   `400 Bad Request`: Invalid query parameter values (e.g., invalid date range, unsupported period).\n    *   `401 Unauthorized`: Invalid or missing admin token.\n    *   `403 Forbidden`: User is not an admin.\n    *   `500 Internal Server Error`: Server-side issue.", "testStrategy": "Verify statistics data is fetched and displayed correctly. Check that charts render accurately based on the data. Test the date range selector to ensure statistics update for different periods. Validate API request (query params) and response structures, including error handling for invalid parameters.", "subtasks": []}]}