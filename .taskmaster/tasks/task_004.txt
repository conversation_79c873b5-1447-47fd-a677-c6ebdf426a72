# Task ID: 4
# Title: Provider: Bid Creation Form and Logic
# Status: done
# Dependencies: None
# Priority: high
# Description: Develop the UI form for providers to create and submit a new bid on a service request. This includes fields for bid amount and description.
# Details:
Create a form component (e.g., `BidCreationForm`). Include input fields for 'Bid amount' (numeric, currency input) and 'Detailed description' (textarea). Use a form library like `react-hook-form` for form state management and validation (e.g., amount is numeric, description length). On submission, call the API client to send a `POST` request to `/api/bids` with the form data (`service_request_id`, `amount`, `description`, `provider_id`). Handle success (show confirmation notification) and validation errors (display error messages next to fields).

# Test Strategy:
Test form submission with valid and invalid data. Verify frontend validation messages appear correctly. Confirm successful bid creation via API call and check the response. Test error handling for API validation errors (422).
