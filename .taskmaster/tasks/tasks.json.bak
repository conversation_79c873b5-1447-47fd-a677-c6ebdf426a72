{"tasks": [{"id": 4, "title": "Provider: Bid Creation Form and Logic", "description": "Develop the UI form for providers to create and submit a new bid on a service request. This includes fields for bid amount and description.", "details": "Create a form component (e.g., `BidCreationForm`). Include input fields for 'Bid amount' (numeric, currency input) and 'Detailed description' (textarea). Use a form library like `react-hook-form` for form state management and validation (e.g., amount is numeric, description length). On submission, call the API client to send a `POST` request to `/api/bids` with the form data (`service_request_id`, `amount`, `description`, `provider_id`). Handle success (show confirmation notification) and validation errors (display error messages next to fields).", "testStrategy": "Test form submission with valid and invalid data. Verify frontend validation messages appear correctly. Confirm successful bid creation via API call and check the response. Test error handling for API validation errors (422).", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 5, "title": "Provider: Bid Dashboard Page Layout", "description": "Design and implement the basic layout for the provider's dashboard where they can view all their submitted bids.", "details": "Create a new page component (e.g., `/provider/dashboard/bids`). Set up the basic page structure including space for filters, sorting controls, and a list/table to display bids. Use the chosen UI library for layout components.", "testStrategy": "Verify the page renders with the correct layout structure.", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 6, "title": "Provider: Fetch and Display Provider Bids", "description": "Implement the logic to fetch and display the list of bids submitted by the currently logged-in provider on their dashboard.", "details": "In the provider bid dashboard component, use React Query to fetch data from `GET /api/bids/provider/{provider_id}` (replace `{provider_id}` with the logged-in user's ID). Display the fetched bids in a list or table component. Each item should show Service Request Title, Bid Amount, Submission Date, and Status. Use visual indicators (e.g., color-coded badges) for status.", "testStrategy": "Verify the dashboard loads and displays the provider's bids fetched from the API. Check that all required bid details are shown correctly. Test pagination if the API returns paginated data.", "priority": "high", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Provider: Implement Provider Bid Filtering and Sorting", "description": "Add filtering and sorting controls to the provider's bid dashboard, allowing them to organize their bids.", "details": "Add UI components for filtering by status (requested, accepted, rejected), date range, and service request. Add controls for sorting by amount, date, and status. Update the React Query fetch call (`GET /api/bids/provider/{provider_id}`) to include the selected filter and sort parameters as query parameters (`status`, `date_from`, `date_to`, `service_request_id`, `sort_by`, `sort_order`). Ensure the UI updates when filters/sort order change.", "testStrategy": "Test each filter and sort option to ensure the displayed bid list updates correctly according to the selected criteria and matches API responses.", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Provider: Implement Bid Update/Withdraw Actions", "description": "Implement the functionality for providers to update the amount and description of their pending bids and to withdraw pending bids.", "details": "Add 'Update' and 'Withdraw' action buttons to bid items on the provider dashboard. Enable these buttons only for bids with 'requested' status. For 'Update', implement a modal or form pre-filled with current bid details, allowing modification of amount and description, then send a `PUT/PATCH` request to `/api/bids/{id}`. For 'Withdraw', implement a confirmation dialog, then send a `DELETE` request to `/api/bids/{id}`. Handle success and error responses, updating the UI accordingly (e.g., removing withdrawn bid, refreshing list).", "testStrategy": "Test updating a pending bid with valid data. Test withdrawing a pending bid. Verify actions are disabled for accepted/rejected bids. Check API calls and UI updates for success and error cases.", "priority": "high", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 9, "title": "Customer: Service Request Detail Page Bid Section", "description": "Integrate the bid viewing interface into the service request detail page for customers, showing all bids received for that specific request.", "details": "Modify the existing Service Request Detail page component (or create one if it doesn't exist). Add a dedicated section to display bids received for the request. This section should be visible only to the customer who owns the service request.", "testStrategy": "Verify the bid section appears correctly on the service request detail page for the customer who created the request.", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 10, "title": "Customer: Fetch and Display Service Request Bids", "description": "Implement fetching and displaying the list of bids associated with a specific service request on the customer's service request detail page.", "details": "In the bid section of the Service Request Detail page, use React Query to fetch data from `GET /api/bids/service-request/{service_request_id}`. Display each bid, including Provider Name (with basic profile info if available), Bid Amount, Proposal Description, Submission Date, and Status indicators. Ensure provider rating/reviews are displayed if provided by the API.", "testStrategy": "Verify the bid list loads and displays bids for the specific service request. Check that all required bid and provider details are shown correctly.", "priority": "high", "dependencies": [9], "status": "pending", "subtasks": []}, {"id": 11, "title": "Customer: Implement Customer Bid Sorting and Comparison UI", "description": "Add sorting options and visual comparison tools to help customers evaluate bids on their service requests.", "details": "Add UI controls to sort bids by amount and submission date. Implement logic to update the fetch call (`GET /api/bids/service-request/{service_request_id}`) with `sort_by` and `sort_order` query parameters. Add visual elements to compare bid amounts, potentially highlighting them against the original service request budget if available.", "testStrategy": "Test sorting by amount and date. Verify the bid list reorders correctly. Check that visual comparison elements (if implemented) are displayed as intended.", "priority": "medium", "dependencies": [10], "status": "pending", "subtasks": []}, {"id": 12, "title": "Customer: Implement Bid Accept/Reject Actions", "description": "Implement the functionality for customers to accept or reject bids received for their service requests.", "details": "Add 'Accept' and 'Reject' action buttons to each bid item on the service request detail page. Implement confirmation dialogs for both actions. On confirmation, send a `PATCH` request to `/api/bids/{id}/status` with the new status (`'accepted'` or `'rejected'`). Handle success and error responses. Update the UI to reflect the new bid status and potentially disable actions on other bids for the same request if one is accepted.", "testStrategy": "Test accepting a bid and rejecting a bid. Verify confirmation dialogs appear. Check API calls and UI updates for success and error cases. Ensure actions are disabled appropriately after a bid is accepted/rejected.", "priority": "high", "dependencies": [10], "status": "pending", "subtasks": []}, {"id": 13, "title": "Admin: Admin Dashboard Layout and Navigation", "description": "Design and implement the basic layout and navigation for the administrator's bid overview dashboard.", "details": "Create a new page component (e.g., `/admin/dashboard/bids`). Set up the page structure, including navigation specific to admin features (if applicable) and areas for displaying the comprehensive bid table and statistics.", "testStrategy": "Verify the admin bid dashboard page loads with the correct layout and navigation elements.", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 14, "title": "Admin: Comprehensive Bid Listing (<PERSON><PERSON>, <PERSON><PERSON>lay, <PERSON>lter, Sort)", "description": "Implement fetching, displaying, filtering, and sorting for the comprehensive list of all bids accessible to administrators.", "details": "In the admin bid dashboard, use React Query to fetch data from `GET /api/admin/bids`. Display the data in a responsive table component (e.g., using Material UI DataGrid or a custom table). Implement advanced filtering UI controls for provider, customer, service request, status, amount range, and date range. Add sorting functionality for table columns. Update the fetch call with corresponding query parameters (`provider_id`, `service_request_id`, `status`, `date_from`, `date_to`, `amount_min`, `amount_max`, `sort_by`, `sort_order`). Implement pagination based on API response.", "testStrategy": "Verify the table loads and displays all bids. Test each filtering option individually and in combination. Test sorting on different columns. Verify pagination works correctly. Check loading and error states.", "priority": "high", "dependencies": [13], "status": "pending", "subtasks": []}, {"id": 15, "title": "Admin: Bid Statistics and Analytics (Fetch, Display Charts)", "description": "Implement fetching and displaying bid statistics and analytics data for administrators using charts and visualizations.", "details": "In the admin bid dashboard, use React Query to fetch data from `GET /api/admin/bids/stats`. Use a charting library like `chart.js` or `recharts` to visualize the statistics (e.g., total bids by status, conversion rates, average bid amounts). Add a date range selector UI that updates the fetch call with `period`, `date_from`, and `date_to` query parameters. Display key metrics as summary numbers.", "testStrategy": "Verify statistics data is fetched and displayed correctly. Check that charts render accurately based on the data. Test the date range selector to ensure statistics update for different periods.", "priority": "high", "dependencies": [13], "status": "pending", "subtasks": []}]}