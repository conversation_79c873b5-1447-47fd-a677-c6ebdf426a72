# Task ID: 15
# Title: Admin: Bid Statistics and Analytics (Fetch, Display Charts)
# Status: pending
# Dependencies: 13
# Priority: high
# Description: Implement fetching and displaying bid statistics and analytics data for administrators using charts and visualizations.
# Details:
In the admin bid dashboard, use React Query to fetch data from `GET /api/admin/bids/stats`. Use a charting library like `chart.js` or `recharts` to visualize the statistics (e.g., total bids by status, conversion rates, average bid amounts). Add a date range selector UI that updates the fetch call with `period`, `date_from`, and `date_to` query parameters. Display key metrics as summary numbers.

**API Interaction Details:**

1.  **Endpoint & Method:**
    *   `GET /api/admin/bids/stats`
2.  **Authentication:**
    *   Admin JWT token required in Authorization header.
3.  **Request Payload:**
    *   N/A (GET request)
4.  **Query Parameters:**
    *   `period`: string (e.g., "daily", "weekly", "monthly", "yearly", "custom" - if 'custom', `date_from` and `date_to` are required)
    *   `date_from`: string (ISO 8601 date, e.g., "2023-01-01" - required if `period` is "custom")
    *   `date_to`: string (ISO 8601 date, e.g., "2023-12-31" - required if `period` is "custom")
    *   `group_by`: string (optional, e.g., "status", "category" - to get breakdowns)
5.  **Response Formats:**
    *   **Success (200 OK):**
        ```json
        {
          "success": true,
          "data": {
            "summary": {
              "totalBids": 1250,
              "totalValueBids": 250000.00,
              "averageBidAmount": 200.00,
              "acceptedBids": 300,
              "acceptanceRate": 0.24
            },
            "bidsByStatus": [
              { "status": "requested", "count": 700, "value": 140000.00 },
              { "status": "accepted", "count": 300, "value": 75000.00 },
              { "status": "rejected", "count": 200, "value": 30000.00 },
              { "status": "withdrawn", "count": 50, "value": 5000.00 }
            ],
            "bidTrends": [
              { "date": "2023-10-01", "count": 50, "averageAmount": 190.00 },
              { "date": "2023-10-02", "count": 55, "averageAmount": 210.00 }
            ]
          },
          "message": "Bid statistics fetched successfully."
        }
        ```
    *   **Error (e.g., 400, 401, 403, 500):**
        ```json
        {
          "success": false,
          "error": {
            "code": "FETCH_BID_STATS_FAILED",
            "message": "Failed to fetch bid statistics.",
            "details": { /* e.g., "Invalid period parameter" */ }
          }
        }
        ```
6.  **Key Status Codes & Scenarios:**
    *   `200 OK`: Statistics fetched successfully.
    *   `400 Bad Request`: Invalid query parameter values (e.g., invalid date range, unsupported period).
    *   `401 Unauthorized`: Invalid or missing admin token.
    *   `403 Forbidden`: User is not an admin.
    *   `500 Internal Server Error`: Server-side issue.

# Test Strategy:
Verify statistics data is fetched and displayed correctly. Check that charts render accurately based on the data. Test the date range selector to ensure statistics update for different periods. Validate API request (query params) and response structures, including error handling for invalid parameters.
