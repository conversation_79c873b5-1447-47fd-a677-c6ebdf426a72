# Task ID: 10
# Title: Customer: Fetch and Display Service Request Bids
# Status: pending
# Dependencies: 9
# Priority: high
# Description: Implement fetching and displaying the list of bids associated with a specific service request on the customer's service request detail page.
# Details:
In the bid section of the Service Request Detail page, use React Query to fetch data from `GET /api/bids/service-request/{service_request_id}`. Display each bid, including Provider Name (with basic profile info if available), Bid Amount, Proposal Description, Submission Date, and Status indicators. Ensure provider rating/reviews are displayed if provided by the API.

**API Interaction Details:**

1.  **Endpoint & Method:**
    *   `GET /api/bids/service-request/{service_request_id}`
        *   `{service_request_id}`: The ID of the service request for which to fetch bids.
2.  **Authentication:**
    *   Customer JWT token required in Authorization header. (The customer must own the service request).
3.  **Request Payload:**
    *   N/A (GET request)
4.  **Query Parameters (Optional for pagination, filtering, sorting - see Task 11):**
    *   `page`: number (e.g., 1, 2, ... for pagination)
    *   `limit`: number (e.g., 10, 20 items per page)
5.  **Response Formats:**
    *   **Success (200 OK):**
        ```json
        {
          "success": true,
          "data": {
            "bids": [
              {
                "id": "bid_uuid",
                "providerId": "provider_uuid",
                "providerName": "Awesome Provider Inc.",
                "providerProfile": {
                  "avatarUrl": "https://example.com/avatar.png",
                  "tagline": "Quality work, on time.",
                  "averageRating": 4.8,
                  "totalReviews": 120
                },
                "amount": 200.00,
                "currency": "USD",
                "proposalDescription": "Detailed proposal for your service request...",
                "submissionDate": "2023-10-25T14:30:00Z",
                "status": "requested"
              }
            ],
            "pagination": {
              "currentPage": 1,
              "totalPages": 2,
              "totalItems": 15,
              "itemsPerPage": 10
            }
          },
          "message": "Bids for service request fetched successfully."
        }
        ```
    *   **Error (e.g., 400, 401, 403, 404, 500):**
        ```json
        {
          "success": false,
          "error": {
            "code": "FETCH_SR_BIDS_FAILED",
            "message": "Failed to fetch bids for the service request.",
            "details": { /* optional additional error details */ }
          }
        }
        ```
6.  **Key Status Codes & Scenarios:**
    *   `200 OK`: Bids fetched successfully.
    *   `401 Unauthorized`: Invalid or missing customer token.
    *   `403 Forbidden`: Customer does not own the service request.
    *   `404 Not Found`: Service request ID not found.
    *   `500 Internal Server Error`: Server-side issue.

# Test Strategy:
Verify the bid list loads and displays bids for the specific service request. Check that all required bid and provider details are shown correctly. Verify API response structure and error handling for various scenarios (e.g., no bids, unauthorized access).
