# Task ID: 14
# Title: Admin: Comprehensive Bid Listing (Fetch, Display, Filter, Sort)
# Status: pending
# Dependencies: 13
# Priority: high
# Description: Implement fetching, displaying, filtering, and sorting for the comprehensive list of all bids accessible to administrators.
# Details:
In the admin bid dashboard, use React Query to fetch data from `GET /api/admin/bids`. Display the data in a responsive table component (e.g., using Material UI DataGrid or a custom table). Implement advanced filtering UI controls for provider, customer, service request, status, amount range, and date range. Add sorting functionality for table columns. Update the fetch call with corresponding query parameters (`provider_id`, `customer_id`, `service_request_id`, `status`, `date_from`, `date_to`, `amount_min`, `amount_max`, `sort_by`, `sort_order`, `search_term`). Implement pagination based on API response.

**API Interaction Details:**

1.  **Endpoint & Method:**
    *   `GET /api/admin/bids`
2.  **Authentication:**
    *   Admin JWT token required in Authorization header.
3.  **Request Payload:**
    *   N/A (GET request)
4.  **Query Parameters:**
    *   `provider_id`: string (UUID of a provider to filter by)
    *   `customer_id`: string (UUID of a customer to filter by)
    *   `service_request_id`: string (UUID of a service request to filter by)
    *   `status`: string (e.g., "requested", "accepted", "rejected", "withdrawn", "completed" - comma-separated for multiple values if API supports)
    *   `date_from`: string (ISO 8601 date, e.g., "2023-01-01")
    *   `date_to`: string (ISO 8601 date, e.g., "2023-12-31")
    *   `amount_min`: number (minimum bid amount)
    *   `amount_max`: number (maximum bid amount)
    *   `sort_by`: string (e.g., "submissionDate", "amount", "status", "providerName", "customerName")
    *   `sort_order`: string ("asc" or "desc")
    *   `page`: number (for pagination)
    *   `limit`: number (items per page for pagination)
    *   `search_term`: string (for general text search across relevant fields)
5.  **Response Formats:**
    *   **Success (200 OK):**
        ```json
        {
          "success": true,
          "data": {
            "bids": [
              {
                "id": "bid_uuid",
                "serviceRequestId": "sr_uuid",
                "serviceRequestTitle": "Title of Service Request",
                "providerId": "provider_uuid",
                "providerName": "Provider X",
                "customerId": "customer_uuid",
                "customerName": "Customer Y",
                "amount": 100.00,
                "currency": "USD",
                "submissionDate": "2023-10-20T09:00:00Z",
                "status": "accepted",
                "updatedAt": "2023-10-21T10:00:00Z"
              }
            ],
            "pagination": {
              "currentPage": 1,
              "totalPages": 10,
              "totalItems": 95,
              "itemsPerPage": 10
            }
          },
          "message": "Admin bids fetched successfully."
        }
        ```
    *   **Error (e.g., 400, 401, 403, 500):**
        ```json
        {
          "success": false,
          "error": {
            "code": "FETCH_ADMIN_BIDS_FAILED",
            "message": "Failed to fetch admin bids.",
            "details": { /* e.g., "Invalid filter parameter" */ }
          }
        }
        ```
6.  **Key Status Codes & Scenarios:**
    *   `200 OK`: Bids fetched successfully.
    *   `400 Bad Request`: Invalid filter/sort parameter values or format.
    *   `401 Unauthorized`: Invalid or missing admin token.
    *   `403 Forbidden`: User is not an admin.
    *   `500 Internal Server Error`: Server-side issue.

# Test Strategy:
Verify the table loads and displays all bids. Test each filtering option individually and in combination. Test sorting on different columns. Verify pagination works correctly. Check loading and error states. Validate API request (query params) and response structures, including error handling.
