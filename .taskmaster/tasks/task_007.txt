# Task ID: 7
# Title: Provider: Implement Provider Bid Filtering and Sorting
# Status: pending
# Dependencies: 6
# Priority: medium
# Description: Add filtering and sorting controls to the provider's bid dashboard, allowing them to organize their bids.
# Details:
Add UI components for filtering by status (requested, accepted, rejected), date range, and service request. Add controls for sorting by amount, date, and status. Update the React Query fetch call (`GET /api/bids/provider/{provider_id}`) to include the selected filter and sort parameters as query parameters (`status`, `date_from`, `date_to`, `service_request_id`, `sort_by`, `sort_order`). Ensure the UI updates when filters/sort order change.

**API Interaction Details (for `GET /api/bids/provider/{provider_id}` with filtering/sorting):**

1.  **Endpoint & Method:**
    *   `GET /api/bids/provider/{provider_id}`
        *   `{provider_id}`: The ID of the currently logged-in provider.
2.  **Authentication:**
    *   Provider JWT token required in Authorization header.
3.  **Request Payload:**
    *   N/A (GET request)
4.  **Query Parameters:**
    *   `status`: string (e.g., "requested", "accepted", "rejected", "withdrawn" - can be comma-separated for multiple values if API supports, e.g., "requested,accepted")
    *   `date_from`: string (ISO 8601 date, e.g., "2023-01-01")
    *   `date_to`: string (ISO 8601 date, e.g., "2023-12-31")
    *   `service_request_id`: string (UUID of a specific service request to filter by)
    *   `sort_by`: string (e.g., "amount", "submissionDate", "status")
    *   `sort_order`: string ("asc" or "desc")
    *   `page`: number (for pagination)
    *   `limit`: number (items per page for pagination)
5.  **Response Formats:**
    *   **Success (200 OK):**
        ```json
        {
          "success": true,
          "data": {
            "bids": [ /* filtered and sorted list of bids */ ],
            "pagination": { /* pagination details */ }
          },
          "message": "Provider bids fetched successfully with filters/sorting."
        }
        ```
    *   **Error (e.g., 400, 401, 403, 500):**
        ```json
        {
          "success": false,
          "error": {
            "code": "INVALID_FILTER_PARAMS",
            "message": "Invalid filter or sort parameters provided.",
            "details": { /* specific field errors */ }
          }
        }
        ```
6.  **Key Status Codes & Scenarios:**
    *   `200 OK`: Bids fetched successfully (filtered/sorted).
    *   `400 Bad Request`: Invalid filter/sort parameter values or format.
    *   `401 Unauthorized`: Invalid or missing provider token.
    *   `403 Forbidden`: User is not a provider.
    *   `500 Internal Server Error`: Server-side issue.

# Test Strategy:
Test each filter and sort option to ensure the displayed bid list updates correctly according to the selected criteria and matches API responses. Verify API query parameters are correctly sent and responses are handled.
