# Task ID: 13
# Title: Admin: Admin Dashboard Layout and Navigation
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Design and implement the basic layout and navigation for the administrator's bid overview dashboard.
# Details:
Create a new page component (e.g., `/admin/dashboard/bids`). Set up the page structure, including navigation specific to admin features (if applicable) and areas for displaying the comprehensive bid table and statistics.

# Test Strategy:
Verify the admin bid dashboard page loads with the correct layout and navigation elements.

# Subtasks:
## 1. Create Admin Dashboard Page Component and Route [pending]
### Dependencies: None
### Description: Set up the initial React/TypeScript component for the admin dashboard (e.g., `AdminBidsDashboardPage.tsx`) and configure its route (e.g., `/admin/dashboard/bids`) within the application's routing system.
### Details:
Create a new file for the page component under the appropriate admin directory structure (e.g., `src/pages/admin/dashboard/bids/AdminBidsDashboardPage.tsx`). Register the route in the main router configuration (e.g., `AppRoutes.tsx`), ensuring it's protected by admin authentication middleware if applicable. The initial component should render a basic placeholder text like 'Admin Bids Dashboard Page'.

## 2. Design and Implement Basic Admin Layout Structure Component [pending]
### Dependencies: 13.1
### Description: Create a reusable `AdminLayout.tsx` component that defines the main structural areas: a header, a sidebar (or top navigation for admin), and a main content area. This layout will wrap the admin dashboard page and other future admin pages.
### Details:
Develop a new `AdminLayout.tsx` component (e.g., in `src/components/admin/layout/AdminLayout.tsx`). Use semantic HTML elements (e.g., `<header>`, `<aside>`, `<main>`). Initially, these areas can be simple `div`s with distinct background colors or borders for visualization. Ensure it follows the established component-based architecture and can accept children props for content.

## 3. Develop Admin-Specific Navigation Component [pending]
### Dependencies: None
### Description: Create a dedicated `AdminNavigation.tsx` component (e.g., `AdminSidebarNav.tsx` or `AdminHeaderNav.tsx`) containing links relevant to admin functionalities. Initially, include a link to 'Bid Overview' and placeholders for future admin sections.
### Details:
This component will house navigation items like 'Dashboard (Bids)', 'User Management', 'Settings', etc. Use React Router's `NavLink` components for navigation to allow active state styling. For now, focus on the structure and a few sample links. Style with basic Tailwind CSS for clarity and interactivity (hover states).

## 4. Integrate Admin Navigation into Admin Layout [pending]
### Dependencies: 13.2, 13.3
### Description: Incorporate the `AdminNavigation` component into the `AdminLayout` component, placing it in the designated sidebar or header area.
### Details:
Import `AdminNavigation` into `AdminLayout.tsx` and render it within the appropriate layout section (e.g., the `<aside>` for a sidebar or `<header>` for top navigation). Ensure props are passed correctly if needed (e.g., for toggling visibility on mobile).

## 5. Define Placeholder Regions for Bid Table and Statistics in Page Component [pending]
### Dependencies: 13.1, 13.2
### Description: Within the main content area of the `AdminBidsDashboardPage` component, define and style distinct placeholder regions where the comprehensive bid table and bid statistics will eventually be displayed.
### Details:
Inside `AdminBidsDashboardPage.tsx`, use `div` elements with clear IDs or class names (e.g., `bid-table-placeholder`, `statistics-placeholder`). Add temporary styling (e.g., borders, background colors, placeholder text like 'Comprehensive Bid Table Area', 'Key Statistics Area') to demarcate these sections. Use Tailwind CSS for basic layout (e.g., flexbox or grid) of these placeholders within the main content area provided by `AdminLayout`.

## 6. Implement Responsive Design for Core Layout and Navigation [pending]
### Dependencies: 13.4
### Description: Apply Tailwind CSS responsive prefixes (e.g., `sm:`, `md:`, `lg:`) to ensure the `AdminLayout` and `AdminNavigation` components adapt gracefully to different screen sizes (desktop, tablet, mobile).
### Details:
Consider how the sidebar might collapse into a hamburger menu on smaller screens, or how header navigation items might stack or hide. Adjust widths, margins, paddings, font sizes, and display properties (e.g., `hidden md:block`) responsively. Implement toggle functionality for mobile navigation if a collapsible sidebar is chosen.

## 7. Apply Consistent Tailwind CSS Styling based on JobON.app Patterns [pending]
### Dependencies: 13.5, 13.6
### Description: Refine the styling of the `AdminLayout`, `AdminNavigation`, and placeholder regions using Tailwind CSS, adhering to the JobON.app's existing design system (colors, fonts, spacing) and utility class patterns for a consistent look and feel.
### Details:
Replace temporary styling with project-consistent colors (e.g., `bg-primary`, `text-neutral-800`), fonts, spacing utilities (e.g., `p-4`, `m-2`), and component styles. Ensure class names are semantic and follow Tailwind best practices. Refer to existing JobON.app components for styling guidance to maintain visual consistency.

## 8. Code Review and Refactor for Adherence to JobON.app Standards [pending]
### Dependencies: 13.7
### Description: Review all newly created components (`AdminBidsDashboardPage.tsx`, `AdminLayout.tsx`, `AdminNavigation.tsx`) for adherence to JobON.app's established React/TypeScript structure, component-based architecture, naming conventions, and code quality standards. Refactor as necessary.
### Details:
Check for proper type definitions (TypeScript), component modularity, reusability, clear prop interfaces, and efficient state management (if any at this stage, e.g., for mobile nav toggle). Ensure file organization aligns with project standards. Remove any commented-out code, console logs, or unused imports. Run linters and formatters.

