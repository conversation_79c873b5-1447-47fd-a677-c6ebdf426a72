# Task ID: 6
# Title: Provider: Fetch and Display Provider Bids
# Status: pending
# Dependencies: 5
# Priority: high
# Description: Implement the logic to fetch and display the list of bids submitted by the currently logged-in provider on their dashboard.
# Details:
In the provider bid dashboard component, use React Query to fetch data from `GET /api/bids/provider/{provider_id}` (replace `{provider_id}` with the logged-in user's ID). Display the fetched bids in a list or table component. Each item should show Service Request Title, Bid Amount, Submission Date, and Status. Use visual indicators (e.g., color-coded badges) for status.

**API Interaction Details:**

1.  **Endpoint & Method:**
    *   `GET /api/bids/provider/{provider_id}`
        *   `{provider_id}`: The ID of the currently logged-in provider.
2.  **Authentication:**
    *   Provider JWT token required in Authorization header.
3.  **Request Payload:**
    *   N/A (GET request)
4.  **Query Parameters (Optional for pagination, filtering, sorting - see Task 7):**
    *   `page`: number (e.g., 1, 2, ... for pagination)
    *   `limit`: number (e.g., 10, 20 items per page)
5.  **Response Formats:**
    *   **Success (200 OK):**
        ```json
        {
          "success": true,
          "data": {
            "bids": [
              {
                "id": "bid_uuid",
                "serviceRequestId": "sr_uuid",
                "serviceRequestTitle": "Title of the Service Request",
                "amount": 150.00,
                "currency": "USD",
                "submissionDate": "2023-10-26T10:00:00Z",
                "status": "requested" // "requested", "accepted", "rejected", "withdrawn"
              }
            ],
            "pagination": {
              "currentPage": 1,
              "totalPages": 5,
              "totalItems": 48,
              "itemsPerPage": 10
            }
          },
          "message": "Provider bids fetched successfully."
        }
        ```
    *   **Error (e.g., 400, 401, 403, 404, 500):**
        ```json
        {
          "success": false,
          "error": {
            "code": "FETCH_PROVIDER_BIDS_FAILED",
            "message": "Failed to fetch provider bids.",
            "details": { /* optional additional error details */ }
          }
        }
        ```
6.  **Key Status Codes & Scenarios:**
    *   `200 OK`: Bids fetched successfully.
    *   `401 Unauthorized`: Invalid or missing provider token.
    *   `403 Forbidden`: User is not a provider or trying to access another provider's bids.
    *   `404 Not Found`: Provider ID not found.
    *   `500 Internal Server Error`: Server-side issue.

# Test Strategy:
Verify the dashboard loads and displays the provider's bids fetched from the API. Check that all required bid details are shown correctly. Test pagination if the API returns paginated data. Verify API response structure and error handling.
