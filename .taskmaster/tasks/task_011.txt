# Task ID: 11
# Title: Customer: Implement Customer Bid Sorting and Comparison UI
# Status: pending
# Dependencies: 10
# Priority: medium
# Description: Add sorting options and visual comparison tools to help customers evaluate bids on their service requests.
# Details:
Add UI controls to sort bids by amount and submission date. Implement logic to update the fetch call (`GET /api/bids/service-request/{service_request_id}`) with `sort_by` and `sort_order` query parameters. Add visual elements to compare bid amounts, potentially highlighting them against the original service request budget if available.

**API Interaction Details (for `GET /api/bids/service-request/{service_request_id}` with sorting):**

1.  **Endpoint & Method:**
    *   `GET /api/bids/service-request/{service_request_id}`
        *   `{service_request_id}`: The ID of the service request.
2.  **Authentication:**
    *   Customer JWT token required in Authorization header.
3.  **Request Payload:**
    *   N/A (GET request)
4.  **Query Parameters:**
    *   `sort_by`: string (e.g., "amount", "submissionDate", "providerRating")
    *   `sort_order`: string ("asc" or "desc")
    *   `page`: number (for pagination)
    *   `limit`: number (items per page for pagination)
5.  **Response Formats:**
    *   **Success (200 OK):**
        ```json
        {
          "success": true,
          "data": {
            "bids": [ /* sorted list of bids for this service request */ ],
            "pagination": { /* pagination details */ }
          },
          "message": "Bids for service request fetched successfully with sorting."
        }
        ```
    *   **Error (e.g., 400, 401, 403, 404, 500):**
        ```json
        {
          "success": false,
          "error": {
            "code": "INVALID_SORT_PARAMS",
            "message": "Invalid sort parameters provided.",
            "details": { /* specific field errors */ }
          }
        }
        ```
6.  **Key Status Codes & Scenarios:**
    *   `200 OK`: Bids fetched successfully (sorted).
    *   `400 Bad Request`: Invalid sort parameter values or format.
    *   `401 Unauthorized`: Invalid or missing customer token.
    *   `403 Forbidden`: Customer does not own the service request.
    *   `404 Not Found`: Service request ID not found.
    *   `500 Internal Server Error`: Server-side issue.

# Test Strategy:
Test sorting by amount and date. Verify the bid list reorders correctly. Check that visual comparison elements (if implemented) are displayed as intended. Verify API query parameters for sorting are correctly sent and responses are handled.
