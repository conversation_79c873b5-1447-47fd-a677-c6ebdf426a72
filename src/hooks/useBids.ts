import { useState, useEffect, useCallback } from 'react';
import { BidService } from '@/services/bidService';
import { useToast } from '@/hooks/use-toast';
import type {
  Bid,
  BidWithProvider,
  Bid<PERSON>ithJob,
  BidFormData,
  BidUpdateData,
  BidFilters,
  BidListResponse,
  BidStats,
  BidStatus
} from '@/types/bid';

interface UseBidsReturn {
  bids: BidWithProvider[] | BidWithJob[];
  loading: boolean;
  error: string | null;
  stats?: BidStats;
  meta?: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    perPage: number;
  };
  refetch: () => Promise<void>;
}

interface UseBidReturn {
  bid: BidWithJob | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseBidActionsReturn {
  submitBid: (jobId: string, bidData: BidFormData) => Promise<boolean>;
  updateBid: (bidId: string, updateData: BidUpdateData) => Promise<boolean>;
  withdrawBid: (bidId: string, reason?: string) => Promise<boolean>;
  acceptBid: (bidId: string, note?: string) => Promise<boolean>;
  rejectBid: (bidId: string, reason?: string) => Promise<boolean>;
  loading: boolean;
}

/**
 * Hook for fetching provider bids with filtering and pagination
 */
export const useProviderBids = (filters?: BidFilters): UseBidsReturn => {
  const [bids, setBids] = useState<BidWithJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<BidStats>();
  const [meta, setMeta] = useState<any>();
  const { toast } = useToast();

  const fetchBids = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await BidService.getProviderBids(filters);
      
      if (response.data) {
        setBids(response.data as BidWithJob[]);
        setStats(response.stats);
        setMeta(response.meta);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to fetch bids';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [filters, toast]);

  useEffect(() => {
    fetchBids();
  }, [fetchBids]);

  return {
    bids,
    loading,
    error,
    stats,
    meta,
    refetch: fetchBids
  };
};

/**
 * Hook for fetching job bids (customer view)
 */
export const useJobBids = (jobId: string, filters?: { sortBy?: 'amount' | 'rating' | 'date'; sortOrder?: 'asc' | 'desc' }): UseBidsReturn => {
  const [bids, setBids] = useState<BidWithProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchBids = useCallback(async () => {
    if (!jobId) return;
    
    try {
      setLoading(true);
      setError(null);
      const response = await BidService.getJobBids(jobId, filters);
      
      if (response.data) {
        setBids(response.data);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to fetch job bids';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [jobId, filters, toast]);

  useEffect(() => {
    fetchBids();
  }, [fetchBids]);

  return {
    bids,
    loading,
    error,
    refetch: fetchBids
  };
};

/**
 * Hook for fetching customer bid history
 */
export const useCustomerBids = (filters?: BidFilters): UseBidsReturn => {
  const [bids, setBids] = useState<BidWithJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<BidStats>();
  const [meta, setMeta] = useState<any>();
  const { toast } = useToast();

  const fetchBids = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await BidService.getCustomerBidHistory(filters);
      
      if (response.data) {
        setBids(response.data as BidWithJob[]);
        setStats(response.stats);
        setMeta(response.meta);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to fetch bid history';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [filters, toast]);

  useEffect(() => {
    fetchBids();
  }, [fetchBids]);

  return {
    bids,
    loading,
    error,
    stats,
    meta,
    refetch: fetchBids
  };
};

/**
 * Hook for fetching admin bids with advanced filtering
 */
export const useAdminBids = (filters?: BidFilters & { page?: number; perPage?: number }): UseBidsReturn => {
  const [bids, setBids] = useState<(BidWithProvider | BidWithJob)[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<BidStats>();
  const [meta, setMeta] = useState<any>();
  const { toast } = useToast();

  const fetchBids = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await BidService.getAdminBids(filters);
      
      if (response.data) {
        setBids(response.data);
        setStats(response.stats);
        setMeta(response.meta);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to fetch admin bids';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [filters, toast]);

  useEffect(() => {
    fetchBids();
  }, [fetchBids]);

  return {
    bids,
    loading,
    error,
    stats,
    meta,
    refetch: fetchBids
  };
};

/**
 * Hook for fetching a single bid by ID
 */
export const useBid = (bidId: string): UseBidReturn => {
  const [bid, setBid] = useState<BidWithJob | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchBid = useCallback(async () => {
    if (!bidId) return;
    
    try {
      setLoading(true);
      setError(null);
      const response = await BidService.getBidById(bidId);
      
      if (response.data) {
        setBid(response.data);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to fetch bid details';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [bidId, toast]);

  useEffect(() => {
    fetchBid();
  }, [fetchBid]);

  return {
    bid,
    loading,
    error,
    refetch: fetchBid
  };
};

/**
 * Hook for bid actions (submit, update, withdraw, accept, reject)
 */
export const useBidActions = (): UseBidActionsReturn => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const submitBid = useCallback(async (jobId: string, bidData: BidFormData): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await BidService.submitBid(jobId, bidData);
      
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Bid submitted successfully'
        });
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to submit bid';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const updateBid = useCallback(async (bidId: string, updateData: BidUpdateData): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await BidService.updateBid(bidId, updateData);
      
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Bid updated successfully'
        });
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to update bid';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const withdrawBid = useCallback(async (bidId: string, reason?: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await BidService.withdrawBid(bidId, reason);
      
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Bid withdrawn successfully'
        });
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to withdraw bid';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const acceptBid = useCallback(async (bidId: string, note?: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await BidService.acceptBid(bidId, note);
      
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Bid accepted successfully'
        });
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to accept bid';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const rejectBid = useCallback(async (bidId: string, reason?: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await BidService.rejectBid(bidId, reason);
      
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Bid rejected successfully'
        });
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to reject bid';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    submitBid,
    updateBid,
    withdrawBid,
    acceptBid,
    rejectBid,
    loading
  };
};

/**
 * Hook for bid statistics and analytics
 */
export const useBidStats = (userType: 'provider' | 'admin', period?: '7d' | '30d' | '90d' | '1y') => {
  const [stats, setStats] = useState<BidStats | null>(null);
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (userType === 'provider') {
        const [statsResponse, analyticsResponse] = await Promise.all([
          BidService.getProviderBidStats(period),
          BidService.getProviderBidAnalytics(period)
        ]);
        setStats(statsResponse.data);
        setAnalytics(analyticsResponse.data);
      } else if (userType === 'admin') {
        const response = await BidService.getAdminBidAnalytics(period);
        setStats(response.data.overview);
        setAnalytics(response.data);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to fetch bid statistics';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [userType, period, toast]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    analytics,
    loading,
    error,
    refetch: fetchStats
  };
};