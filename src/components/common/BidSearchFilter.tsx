import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { BidFilters, BidSortOptions, BidStatus } from '@/types/bid';
import {
  Search,
  Filter,
  X,
  Calendar as CalendarIcon,
  DollarSign,
  SlidersHorizontal,
  RotateCcw,
  ChevronDown,
  MapPin,
  Star,
  Clock
} from 'lucide-react';
import { format } from 'date-fns';

interface BidSearchFilterProps {
  onFiltersChange: (filters: BidFilters) => void;
  onSortChange: (sort: BidSortOptions) => void;
  initialFilters?: Partial<BidFilters>;
  initialSort?: BidSortOptions;
  userRole?: 'customer' | 'provider' | 'admin';
  showAdvancedFilters?: boolean;
  className?: string;
}

interface FilterState extends BidFilters {
  searchQuery: string;
  amountRange: [number, number];
  ratingRange: [number, number];
}

const DEFAULT_FILTERS: FilterState = {
  searchQuery: '',
  status: undefined,
  providerId: undefined,
  jobId: undefined,
  customerId: undefined,
  minAmount: undefined,
  maxAmount: undefined,
  dateFrom: undefined,
  dateTo: undefined,
  amountRange: [0, 10000],
  ratingRange: [0, 5],
  startDate: undefined,
  endDate: undefined,
  location: undefined,
  category: undefined,
  providerRating: undefined
};

const DEFAULT_SORT: BidSortOptions = {
  field: 'createdAt',
  direction: 'desc'
};

const BID_STATUSES: { value: BidStatus; label: string; color: string }[] = [
  { value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'accepted', label: 'Accepted', color: 'bg-green-100 text-green-800' },
  { value: 'rejected', label: 'Rejected', color: 'bg-red-100 text-red-800' },
  { value: 'withdrawn', label: 'Withdrawn', color: 'bg-gray-100 text-gray-800' }
];

const SORT_OPTIONS = [
  { value: 'createdAt', label: 'Date Created' },
  { value: 'updatedAt', label: 'Last Updated' },
  { value: 'amount', label: 'Bid Amount' },
  { value: 'providerRating', label: 'Provider Rating' }
];

const JOB_CATEGORIES = [
  'Plumbing',
  'Electrical',
  'Carpentry',
  'Painting',
  'Cleaning',
  'Landscaping',
  'HVAC',
  'Roofing',
  'Flooring',
  'Kitchen Renovation',
  'Bathroom Renovation',
  'General Maintenance'
];

export const BidSearchFilter: React.FC<BidSearchFilterProps> = ({
  onFiltersChange,
  onSortChange,
  initialFilters = {},
  initialSort = DEFAULT_SORT,
  userRole = 'admin',
  showAdvancedFilters = true,
  className
}) => {
  const [filters, setFilters] = useState<FilterState>({
    ...DEFAULT_FILTERS,
    ...initialFilters
  });
  const [sort, setSort] = useState<BidSortOptions>(initialSort);
  const [showFilters, setShowFilters] = useState(false);
  const [activeFilterCount, setActiveFilterCount] = useState(0);

  // Debounced search
  const [searchDebounceTimer, setSearchDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, searchQuery: value }));
    
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
    }
    
    const timer = setTimeout(() => {
      applyFilters({ ...filters, searchQuery: value });
    }, 300);
    
    setSearchDebounceTimer(timer);
  };

  const applyFilters = useCallback((newFilters: FilterState) => {
    const apiFilters: BidFilters = {
      status: newFilters.status,
      providerId: newFilters.providerId,
      jobId: newFilters.jobId,
      customerId: newFilters.customerId,
      minAmount: newFilters.amountRange[0] > 0 ? newFilters.amountRange[0] : undefined,
      maxAmount: newFilters.amountRange[1] < 10000 ? newFilters.amountRange[1] : undefined,
      startDate: newFilters.startDate,
      endDate: newFilters.endDate,
      location: newFilters.location,
      category: newFilters.category,
      providerRating: newFilters.ratingRange[0] > 0 ? newFilters.ratingRange[0] : undefined
    };
    
    // Add search query to filters if it exists
    if (newFilters.searchQuery.trim()) {
      (apiFilters as any).search = newFilters.searchQuery.trim();
    }
    
    onFiltersChange(apiFilters);
  }, [onFiltersChange]);

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // Apply filters immediately for non-search fields
    if (key !== 'searchQuery') {
      applyFilters(newFilters);
    }
  };

  const handleSortChange = (field: string, direction?: 'asc' | 'desc') => {
    const newSort: BidSortOptions = {
      field: field as any,
      direction: direction || (sort.field === field && sort.direction === 'desc' ? 'asc' : 'desc')
    };
    setSort(newSort);
    onSortChange(newSort);
  };

  const resetFilters = () => {
    setFilters(DEFAULT_FILTERS);
    setSort(DEFAULT_SORT);
    applyFilters(DEFAULT_FILTERS);
    onSortChange(DEFAULT_SORT);
  };

  const removeFilter = (key: keyof FilterState) => {
    const newFilters = { ...filters };
    
    if (key === 'amountRange') {
      newFilters.amountRange = [0, 10000];
    } else if (key === 'ratingRange') {
      newFilters.ratingRange = [0, 5];
    } else {
      (newFilters as any)[key] = undefined;
    }
    
    setFilters(newFilters);
    applyFilters(newFilters);
  };

  // Count active filters
  useEffect(() => {
    let count = 0;
    if (filters.searchQuery.trim()) count++;
    if (filters.status) count++;
    if (filters.amountRange[0] > 0 || filters.amountRange[1] < 10000) count++;
    if (filters.ratingRange[0] > 0) count++;
    if (filters.startDate || filters.endDate) count++;
    if (filters.location) count++;
    if (filters.category) count++;
    
    setActiveFilterCount(count);
  }, [filters]);

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Quick Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search bids, jobs, or providers..."
            value={filters.searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Sort Dropdown */}
        <Select
          value={`${sort.field}-${sort.direction}`}
          onValueChange={(value) => {
            const [field, direction] = value.split('-') as [string, 'asc' | 'desc'];
            handleSortChange(field, direction);
          }}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            {SORT_OPTIONS.map((option) => (
              <React.Fragment key={option.value}>
                <SelectItem value={`${option.value}-desc`}>
                  {option.label} (Newest)
                </SelectItem>
                <SelectItem value={`${option.value}-asc`}>
                  {option.label} (Oldest)
                </SelectItem>
              </React.Fragment>
            ))}
          </SelectContent>
        </Select>

        {/* Filter Toggle */}
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="relative"
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {activeFilterCount > 0 && (
            <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
              {activeFilterCount}
            </Badge>
          )}
          <ChevronDown className={`h-4 w-4 ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" onClick={resetFilters}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.searchQuery.trim() && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: "{filters.searchQuery}"
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => handleFilterChange('searchQuery', '')}
              />
            </Badge>
          )}
          
          {filters.status && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Status: {BID_STATUSES.find(s => s.value === filters.status)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => removeFilter('status')}
              />
            </Badge>
          )}
          
          {(filters.amountRange[0] > 0 || filters.amountRange[1] < 10000) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Amount: {formatCurrency(filters.amountRange[0])} - {formatCurrency(filters.amountRange[1])}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => removeFilter('amountRange')}
              />
            </Badge>
          )}
          
          {filters.ratingRange[0] > 0 && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Rating: {filters.ratingRange[0]}+ stars
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => removeFilter('ratingRange')}
              />
            </Badge>
          )}
          
          {filters.category && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Category: {filters.category}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => removeFilter('category')}
              />
            </Badge>
          )}
          
          {filters.location && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Location: {filters.location}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => removeFilter('location')}
              />
            </Badge>
          )}
          
          {(filters.startDate || filters.endDate) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Date Range
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => {
                  handleFilterChange('startDate', undefined);
                  handleFilterChange('endDate', undefined);
                }}
              />
            </Badge>
          )}
        </div>
      )}

      {/* Advanced Filters Panel */}
      {showFilters && showAdvancedFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SlidersHorizontal className="h-5 w-5" />
              Advanced Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Status Filter */}
              <div className="space-y-2">
                <Label>Status</Label>
                <Select
                  value={filters.status || ''}
                  onValueChange={(value) => handleFilterChange('status', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    {BID_STATUSES.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${status.color.split(' ')[0]}`} />
                          {status.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Category Filter */}
              <div className="space-y-2">
                <Label>Category</Label>
                <Select
                  value={filters.category || ''}
                  onValueChange={(value) => handleFilterChange('category', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All categories</SelectItem>
                    {JOB_CATEGORIES.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Location Filter */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location
                </Label>
                <Input
                  placeholder="Enter city or zip code"
                  value={filters.location || ''}
                  onChange={(e) => handleFilterChange('location', e.target.value || undefined)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Amount Range */}
              <div className="space-y-4">
                <Label className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Bid Amount Range
                </Label>
                <div className="px-3">
                  <Slider
                    value={filters.amountRange}
                    onValueChange={(value) => handleFilterChange('amountRange', value as [number, number])}
                    max={10000}
                    min={0}
                    step={100}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-gray-500 mt-2">
                    <span>{formatCurrency(filters.amountRange[0])}</span>
                    <span>{formatCurrency(filters.amountRange[1])}</span>
                  </div>
                </div>
              </div>

              {/* Provider Rating */}
              <div className="space-y-4">
                <Label className="flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  Minimum Provider Rating
                </Label>
                <div className="px-3">
                  <Slider
                    value={filters.ratingRange}
                    onValueChange={(value) => handleFilterChange('ratingRange', value as [number, number])}
                    max={5}
                    min={0}
                    step={0.5}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-gray-500 mt-2">
                    <span>{filters.ratingRange[0]} stars</span>
                    <span>{filters.ratingRange[1]} stars</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Date Range */}
            <div className="space-y-4">
              <Label className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Date Range
              </Label>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="space-y-2">
                  <Label className="text-sm text-gray-600">From</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {filters.startDate ? format(filters.startDate, 'PPP') : 'Select start date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={filters.startDate}
                        onSelect={(date) => handleFilterChange('startDate', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-sm text-gray-600">To</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {filters.endDate ? format(filters.endDate, 'PPP') : 'Select end date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={filters.endDate}
                        onSelect={(date) => handleFilterChange('endDate', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};