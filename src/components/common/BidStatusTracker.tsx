import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { getBidById } from '@/services/bidService';
import { Bid, BidStatus } from '@/types/bid';
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Calendar,
  DollarSign,
  MessageSquare,
  FileText,
  MapPin,
  Star,
  Loader2,
  RefreshCw,
  ArrowRight,
  Info
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { format, formatDistanceToNow } from 'date-fns';

interface BidStatusTrackerProps {
  bidId: string;
  onStatusChange?: (status: BidStatus) => void;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

interface StatusStep {
  status: BidStatus;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgColor: string;
  isCompleted: boolean;
  isCurrent: boolean;
  timestamp?: string;
}

interface BidTimeline {
  id: string;
  status: BidStatus;
  timestamp: string;
  description: string;
  actor?: {
    name: string;
    role: 'customer' | 'provider' | 'system';
    avatar?: string;
  };
}

const STATUS_FLOW: Record<BidStatus, number> = {
  pending: 0,
  accepted: 1,
  rejected: 1,
  withdrawn: 1
};

const STATUS_CONFIG = {
  pending: {
    label: 'Pending Review',
    description: 'Waiting for customer response',
    icon: Clock,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    borderColor: 'border-yellow-200'
  },
  accepted: {
    label: 'Accepted',
    description: 'Bid has been accepted by customer',
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    borderColor: 'border-green-200'
  },
  rejected: {
    label: 'Rejected',
    description: 'Bid was not selected by customer',
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    borderColor: 'border-red-200'
  },
  withdrawn: {
    label: 'Withdrawn',
    description: 'Bid was withdrawn by provider',
    icon: AlertCircle,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    borderColor: 'border-gray-200'
  }
};

export const BidStatusTracker: React.FC<BidStatusTrackerProps> = ({
  bidId,
  onStatusChange,
  showActions = false,
  compact = false,
  className
}) => {
  const [bid, setBid] = useState<Bid | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeline, setTimeline] = useState<BidTimeline[]>([]);
  const { toast } = useToast();
  const { user, isAuthenticated, token } = useAuth();

  const fetchBid = async (showRefreshLoader = false) => {
    if (showRefreshLoader) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      const response = await getBidById(bidId);
      
      if (response.isSuccess && response.data) {
        setBid(response.data);
        generateTimeline(response.data);
        
        if (onStatusChange) {
          onStatusChange(response.data.status);
        }
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load bid details',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Failed to fetch bid:', error);
      toast({
        title: 'Error',
        description: 'Failed to load bid details',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const generateTimeline = (bidData: Bid) => {
    const events: BidTimeline[] = [
      {
        id: 'created',
        status: 'pending',
        timestamp: bidData.createdAt,
        description: 'Bid submitted',
        actor: bidData.provider ? {
          name: `${bidData.provider.firstName} ${bidData.provider.lastName}`,
          role: 'provider',
          avatar: bidData.provider.avatar
        } : undefined
      }
    ];

    // Add status change event if not pending
    if (bidData.status !== 'pending') {
      const statusConfig = STATUS_CONFIG[bidData.status];
      events.push({
        id: 'status_change',
        status: bidData.status,
        timestamp: bidData.updatedAt,
        description: statusConfig.description,
        actor: bidData.status === 'withdrawn' ? {
          name: `${bidData.provider?.firstName} ${bidData.provider?.lastName}`,
          role: 'provider',
          avatar: bidData.provider?.avatar
        } : {
          name: 'Customer',
          role: 'customer'
        }
      });
    }

    setTimeline(events);
  };

  useEffect(() => {
    fetchBid();
  }, [bidId]);

  const getStatusSteps = (): StatusStep[] => {
    if (!bid) return [];

    const currentStatusIndex = STATUS_FLOW[bid.status];
    
    return [
      {
        status: 'pending',
        ...STATUS_CONFIG.pending,
        isCompleted: true,
        isCurrent: bid.status === 'pending',
        timestamp: bid.createdAt
      },
      {
        status: bid.status === 'accepted' ? 'accepted' : bid.status === 'rejected' ? 'rejected' : 'withdrawn',
        ...STATUS_CONFIG[bid.status === 'accepted' ? 'accepted' : bid.status === 'rejected' ? 'rejected' : 'withdrawn'],
        isCompleted: bid.status !== 'pending',
        isCurrent: bid.status !== 'pending',
        timestamp: bid.status !== 'pending' ? bid.updatedAt : undefined
      }
    ];
  };

  const getProgressPercentage = (): number => {
    if (!bid) return 0;
    
    switch (bid.status) {
      case 'pending':
        return 50;
      case 'accepted':
      case 'rejected':
      case 'withdrawn':
        return 100;
      default:
        return 0;
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getTimeAgo = (date: string): string => {
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  };

  const refresh = () => {
    fetchBid(true);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center h-32">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (!bid) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center h-32">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Bid not found</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const statusSteps = getStatusSteps();
  const currentConfig = STATUS_CONFIG[bid.status];
  const CurrentIcon = currentConfig.icon;

  if (compact) {
    return (
      <Card className={`${className} ${currentConfig.borderColor} border-2`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full ${currentConfig.bgColor}`}>
                <CurrentIcon className={`h-4 w-4 ${currentConfig.color}`} />
              </div>
              <div>
                <p className="font-medium text-gray-900">{currentConfig.label}</p>
                <p className="text-sm text-gray-600">{getTimeAgo(bid.updatedAt)}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="font-semibold text-gray-900">{formatCurrency(bid.amount)}</p>
              <Progress value={getProgressPercentage()} className="w-20 h-2 mt-1" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <div className={`p-2 rounded-full ${currentConfig.bgColor}`}>
              <CurrentIcon className={`h-5 w-5 ${currentConfig.color}`} />
            </div>
            Bid Status Tracker
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className={`p-4 rounded-lg border-2 ${currentConfig.borderColor} ${currentConfig.bgColor}`}>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <CurrentIcon className={`h-5 w-5 ${currentConfig.color}`} />
              <span className={`font-semibold ${currentConfig.color}`}>
                {currentConfig.label}
              </span>
            </div>
            <Badge variant="outline" className={currentConfig.color}>
              {formatCurrency(bid.amount)}
            </Badge>
          </div>
          <p className="text-sm text-gray-700">{currentConfig.description}</p>
          <p className="text-xs text-gray-500 mt-1">
            Last updated {getTimeAgo(bid.updatedAt)}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600">
            <span>Progress</span>
            <span>{getProgressPercentage()}%</span>
          </div>
          <Progress value={getProgressPercentage()} className="h-2" />
        </div>

        {/* Status Steps */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 flex items-center gap-2">
            <Info className="h-4 w-4" />
            Status Timeline
          </h4>
          
          <div className="space-y-3">
            {statusSteps.map((step, index) => {
              const StepIcon = step.icon;
              
              return (
                <div key={step.status} className="flex items-start gap-3">
                  <div className="flex flex-col items-center">
                    <div className={`p-2 rounded-full border-2 ${
                      step.isCompleted 
                        ? `${step.bgColor} ${step.color} border-current` 
                        : 'bg-gray-100 text-gray-400 border-gray-300'
                    }`}>
                      <StepIcon className="h-4 w-4" />
                    </div>
                    {index < statusSteps.length - 1 && (
                      <div className={`w-0.5 h-8 mt-2 ${
                        step.isCompleted ? 'bg-gray-300' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0 pb-4">
                    <div className="flex items-center justify-between">
                      <p className={`font-medium ${
                        step.isCompleted ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {step.label}
                      </p>
                      {step.timestamp && (
                        <span className="text-xs text-gray-500">
                          {format(new Date(step.timestamp), 'MMM d, h:mm a')}
                        </span>
                      )}
                    </div>
                    <p className={`text-sm mt-1 ${
                      step.isCompleted ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      {step.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Bid Details */}
        <div className="border-t pt-4 space-y-3">
          <h4 className="font-medium text-gray-900">Bid Details</h4>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">Amount:</span>
              <span className="font-medium">{formatCurrency(bid.amount)}</span>
            </div>
            
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">Submitted:</span>
              <span className="font-medium">{format(new Date(bid.createdAt), 'MMM d, yyyy')}</span>
            </div>
          </div>
          
          {bid.description && (
            <div>
              <div className="flex items-center gap-2 mb-2">
                <FileText className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Description:</span>
              </div>
              <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
                {bid.description}
              </p>
            </div>
          )}
        </div>

        {/* Job & Provider Info */}
        <div className="border-t pt-4 space-y-4">
          {bid.job && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Job Information</h4>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="font-medium text-gray-900">{bid.job.title}</p>
                {bid.job.location && (
                  <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                    <MapPin className="h-3 w-3" />
                    {bid.job.location}
                  </p>
                )}
              </div>
            </div>
          )}
          
          {bid.provider && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Provider Information</h4>
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={bid.provider.avatar} />
                    <AvatarFallback>
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-gray-900">
                      {bid.provider.firstName} {bid.provider.lastName}
                    </p>
                    {bid.provider.rating && (
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 text-yellow-500 fill-current" />
                        <span className="text-sm text-gray-600">
                          {bid.provider.rating.toFixed(1)} rating
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Timeline Events */}
        {timeline.length > 0 && (
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-3">Activity Timeline</h4>
            <div className="space-y-3">
              {timeline.map((event, index) => (
                <div key={event.id} className="flex items-start gap-3">
                  <div className="flex flex-col items-center">
                    <div className="p-1.5 rounded-full bg-blue-100">
                      <div className="w-2 h-2 bg-blue-600 rounded-full" />
                    </div>
                    {index < timeline.length - 1 && (
                      <div className="w-0.5 h-6 bg-gray-200 mt-1" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900">
                        {event.description}
                      </p>
                      <span className="text-xs text-gray-500">
                        {getTimeAgo(event.timestamp)}
                      </span>
                    </div>
                    {event.actor && (
                      <p className="text-xs text-gray-600 mt-1">
                        by {event.actor.name}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};