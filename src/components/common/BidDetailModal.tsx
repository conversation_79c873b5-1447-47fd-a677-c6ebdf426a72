import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { getBidById, acceptBid, rejectBid, withdrawBid } from '@/services/bidService';
import { Bid } from '@/types/bid';
import { useAuth } from '@/features/auth/hooks/useAuth';
import {
  Star,
  MapPin,
  Calendar,
  User,
  Check,
  X,
  Loader2,
  DollarSign,
  FileText,
  Clock,
  Building
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface BidDetailModalProps {
  bidId: string | null;
  isOpen: boolean;
  onClose: () => void;
  onBidUpdated?: (bid: Bid) => void;
  userRole?: 'customer' | 'provider' | 'admin';
}

export const BidDetailModal: React.FC<BidDetailModalProps> = ({
  bidId,
  isOpen,
  onClose,
  onBidUpdated,
  userRole
}) => {
  const [bid, setBid] = useState<Bid | null>(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const { toast } = useToast();
  const { user: currentUser } = useAuth();

  useEffect(() => {
    if (bidId && isOpen) {
      fetchBidDetails();
    }
  }, [bidId, isOpen]);

  const fetchBidDetails = async () => {
    if (!bidId) return;
    
    setLoading(true);
    try {
      const response = await getBidById(bidId);
      
      if (response.isSuccess && response.data) {
        setBid(response.data);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch bid details',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptBid = async () => {
    if (!bid) return;
    
    setActionLoading(true);
    try {
      const response = await acceptBid(bid.id);
      
      if (response.isSuccess) {
        toast({
          title: 'Bid Accepted',
          description: 'The bid has been successfully accepted.',
        });
        const updatedBid = { ...bid, status: 'accepted' as const };
        setBid(updatedBid);
        onBidUpdated?.(updatedBid);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to accept bid',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleRejectBid = async () => {
    if (!bid) return;
    
    setActionLoading(true);
    try {
      const response = await rejectBid(bid.id);
      
      if (response.isSuccess) {
        toast({
          title: 'Bid Rejected',
          description: 'The bid has been successfully rejected.',
        });
        const updatedBid = { ...bid, status: 'rejected' as const };
        setBid(updatedBid);
        onBidUpdated?.(updatedBid);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to reject bid',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleWithdrawBid = async () => {
    if (!bid) return;
    
    setActionLoading(true);
    try {
      const response = await withdrawBid(bid.id);
      
      if (response.isSuccess) {
        toast({
          title: 'Bid Withdrawn',
          description: 'Your bid has been successfully withdrawn.',
        });
        const updatedBid = { ...bid, status: 'withdrawn' as const };
        setBid(updatedBid);
        onBidUpdated?.(updatedBid);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to withdraw bid',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const getBidStatusColor = (status: string): string => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'withdrawn': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const canAcceptReject = userRole === 'customer' && bid?.status === 'pending';
  const canWithdraw = userRole === 'provider' && bid?.status === 'pending' && bid?.providerId === currentUser?.id;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Bid Details
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : bid ? (
          <div className="space-y-6">
            {/* Bid Status and Amount */}
            <div className="flex justify-between items-start">
              <div>
                <Badge className={getBidStatusColor(bid.status)} size="lg">
                  {bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
                </Badge>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-green-600 flex items-center gap-1">
                  <DollarSign className="h-8 w-8" />
                  {formatCurrency(bid.amount)}
                </div>
              </div>
            </div>

            {/* Job Information */}
            {bid.job && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Job Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <h3 className="font-semibold text-lg">{bid.job.title}</h3>
                    <p className="text-gray-600">{bid.job.description}</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-gray-500" />
                      <span>Service Type: {bid.job.serviceType}</span>
                    </div>
                    {bid.job.location && (
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span>Location: {bid.job.location}</span>
                      </div>
                    )}
                    {bid.job.scheduledDate && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span>Scheduled: {formatDate(bid.job.scheduledDate)}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Provider Information */}
            {bid.provider && (
              <Card>
                <CardHeader>
                  <CardTitle>Service Provider</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start gap-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={bid.provider.avatar} />
                      <AvatarFallback>
                        <User className="h-8 w-8" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">
                        {bid.provider.firstName} {bid.provider.lastName}
                      </h3>
                      {bid.provider.businessName && (
                        <p className="text-gray-600 mb-2">{bid.provider.businessName}</p>
                      )}
                      <div className="flex items-center gap-2 mb-3">
                        <div className="flex items-center">
                          {renderStars(bid.provider.rating || 0)}
                        </div>
                        <span className="text-sm text-gray-600">
                          ({bid.provider.rating?.toFixed(1) || '0.0'}) Rating
                        </span>
                      </div>
                      {bid.provider.specialty && bid.provider.specialty.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {bid.provider.specialty.map((spec, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {spec}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Customer Information */}
            {bid.customer && userRole !== 'customer' && (
              <Card>
                <CardHeader>
                  <CardTitle>Customer</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={bid.customer.avatar} />
                      <AvatarFallback>
                        <User className="h-6 w-6" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold">
                        {bid.customer.firstName} {bid.customer.lastName}
                      </h3>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Bid Description */}
            <Card>
              <CardHeader>
                <CardTitle>Proposal Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {bid.description}
                </p>
              </CardContent>
            </Card>

            {/* Timeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center text-sm">
                  <span className="font-medium">Submitted:</span>
                  <span>{formatDate(bid.submittedAt)}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="font-medium">Last Updated:</span>
                  <span>{formatDate(bid.updatedAt)}</span>
                </div>
                {bid.acceptedAt && (
                  <div className="flex justify-between items-center text-sm text-green-600">
                    <span className="font-medium">Accepted:</span>
                    <span>{formatDate(bid.acceptedAt)}</span>
                  </div>
                )}
                {bid.rejectedAt && (
                  <div className="flex justify-between items-center text-sm text-red-600">
                    <span className="font-medium">Rejected:</span>
                    <span>{formatDate(bid.rejectedAt)}</span>
                  </div>
                )}
                {bid.withdrawnAt && (
                  <div className="flex justify-between items-center text-sm text-gray-600">
                    <span className="font-medium">Withdrawn:</span>
                    <span>{formatDate(bid.withdrawnAt)}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Action Buttons */}
            {(canAcceptReject || canWithdraw) && (
              <div className="flex justify-end gap-3 pt-4 border-t">
                {canWithdraw && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="outline"
                        disabled={actionLoading}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4 mr-1" />
                        Withdraw Bid
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Withdraw Bid</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to withdraw this bid? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleWithdrawBid}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          Withdraw Bid
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}

                {canAcceptReject && (
                  <>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          disabled={actionLoading}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Reject Bid</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to reject this bid? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleRejectBid}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            Reject Bid
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          disabled={actionLoading}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {actionLoading ? (
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          ) : (
                            <Check className="h-4 w-4 mr-1" />
                          )}
                          Accept Bid
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Accept Bid</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to accept this bid for {formatCurrency(bid.amount)}? This will automatically reject all other bids for this job.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleAcceptBid}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Accept Bid
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">Bid not found</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};